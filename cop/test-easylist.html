<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EasyList Ad Blocking Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .ad-banner {
            background: #ff6b6b;
            color: white;
            padding: 20px;
            text-align: center;
            margin: 10px 0;
        }
        .advertisement {
            background: #4ecdc4;
            color: white;
            padding: 15px;
            margin: 10px 0;
        }
        .sponsored {
            background: #45b7d1;
            color: white;
            padding: 10px;
            margin: 10px 0;
        }
        .normal-content {
            background: #96ceb4;
            padding: 15px;
            margin: 10px 0;
        }
        .status {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #333;
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="status" id="status">
        EasyList Status: Loading...
    </div>

    <h1>EasyList Ad Blocking Test Page</h1>
    <p>This page contains various elements that should be blocked by EasyList rules.</p>

    <div class="test-section">
        <h2>CSS Selector Tests</h2>
        <p>These elements should be hidden by EasyList CSS rules:</p>
        
        <div class="ad-banner">
            This is an ad banner (class="ad-banner") - Should be BLOCKED
        </div>
        
        <div class="advertisement">
            This is an advertisement (class="advertisement") - Should be BLOCKED
        </div>
        
        <div class="sponsored">
            This is sponsored content (class="sponsored") - Should be BLOCKED
        </div>
        
        <div id="ad-container">
            This has id="ad-container" - Should be BLOCKED
        </div>
        
        <div data-ad="true">
            This has data-ad attribute - Should be BLOCKED
        </div>
        
        <div class="normal-content">
            This is normal content - Should be VISIBLE
        </div>
    </div>

    <div class="test-section">
        <h2>Network Request Tests</h2>
        <p>These requests should be blocked by EasyList network rules:</p>
        
        <img src="https://doubleclick.net/test-ad.gif" alt="DoubleClick Ad" style="width: 100px; height: 50px; background: red;">
        <br>
        <img src="https://googleadservices.com/banner.jpg" alt="Google Ad Services" style="width: 100px; height: 50px; background: red;">
        <br>
        <img src="https://googlesyndication.com/ad.png" alt="Google Syndication" style="width: 100px; height: 50px; background: red;">
        
        <p><em>Note: The images above should fail to load if network blocking is working.</em></p>
    </div>

    <div class="test-section">
        <h2>JavaScript Injection Tests</h2>
        <p>Testing fetch and XMLHttpRequest blocking:</p>
        
        <button onclick="testFetch()">Test Fetch Request</button>
        <button onclick="testXHR()">Test XMLHttpRequest</button>
        <button onclick="testNormalRequest()">Test Normal Request</button>
        
        <div id="request-results"></div>
    </div>

    <div class="test-section">
        <h2>Dynamic Content Tests</h2>
        <p>Testing dynamic ad insertion:</p>
        
        <button onclick="addDynamicAd()">Add Dynamic Ad</button>
        <button onclick="addNormalContent()">Add Normal Content</button>
        
        <div id="dynamic-content"></div>
    </div>

    <script>
        // Update status
        function updateStatus() {
            const statusEl = document.getElementById('status');
            const blockedAds = document.querySelectorAll('.ad-banner, .advertisement, .sponsored, #ad-container, [data-ad]');
            let hiddenCount = 0;
            
            blockedAds.forEach(ad => {
                if (window.getComputedStyle(ad).display === 'none' || 
                    window.getComputedStyle(ad).visibility === 'hidden' ||
                    window.getComputedStyle(ad).opacity === '0') {
                    hiddenCount++;
                }
            });
            
            statusEl.innerHTML = `
                EasyList Status: Active<br>
                Elements Hidden: ${hiddenCount}/${blockedAds.length}<br>
                Time: ${new Date().toLocaleTimeString()}
            `;
        }

        // Test fetch requests
        function testFetch() {
            const resultsEl = document.getElementById('request-results');
            
            // This should be blocked
            fetch('https://doubleclick.net/test-ad')
                .then(response => {
                    resultsEl.innerHTML += '<p style="color: red;">❌ Fetch to doubleclick.net succeeded (should be blocked)</p>';
                })
                .catch(error => {
                    resultsEl.innerHTML += '<p style="color: green;">✅ Fetch to doubleclick.net blocked: ' + error.message + '</p>';
                });
        }

        // Test XMLHttpRequest
        function testXHR() {
            const resultsEl = document.getElementById('request-results');
            const xhr = new XMLHttpRequest();
            
            try {
                xhr.open('GET', 'https://googleadservices.com/test');
                xhr.onload = function() {
                    resultsEl.innerHTML += '<p style="color: red;">❌ XHR to googleadservices.com succeeded (should be blocked)</p>';
                };
                xhr.onerror = function() {
                    resultsEl.innerHTML += '<p style="color: green;">✅ XHR to googleadservices.com blocked</p>';
                };
                xhr.send();
            } catch (error) {
                resultsEl.innerHTML += '<p style="color: green;">✅ XHR to googleadservices.com blocked: ' + error.message + '</p>';
            }
        }

        // Test normal request (should work)
        function testNormalRequest() {
            const resultsEl = document.getElementById('request-results');
            
            fetch('https://httpbin.org/get')
                .then(response => response.json())
                .then(data => {
                    resultsEl.innerHTML += '<p style="color: blue;">ℹ️ Normal request to httpbin.org succeeded</p>';
                })
                .catch(error => {
                    resultsEl.innerHTML += '<p style="color: orange;">⚠️ Normal request failed: ' + error.message + '</p>';
                });
        }

        // Add dynamic ad content
        function addDynamicAd() {
            const container = document.getElementById('dynamic-content');
            const ad = document.createElement('div');
            ad.className = 'ad-banner';
            ad.innerHTML = 'Dynamic Ad - Should be blocked immediately';
            container.appendChild(ad);
            
            setTimeout(updateStatus, 100);
        }

        // Add normal content
        function addNormalContent() {
            const container = document.getElementById('dynamic-content');
            const content = document.createElement('div');
            content.className = 'normal-content';
            content.innerHTML = 'Dynamic Normal Content - Should remain visible';
            container.appendChild(content);
        }

        // Update status every 2 seconds
        setInterval(updateStatus, 2000);
        
        // Initial status update
        setTimeout(updateStatus, 1000);
        
        console.log('🧪 EasyList test page loaded');
    </script>
</body>
</html>
