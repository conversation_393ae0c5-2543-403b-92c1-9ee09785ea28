//
//  EasyListService.swift
//  cop
//
//  Created by Augment Agent on 2025/6/16.
//

import Foundation
import OSLog
import Combine

/// EasyList订阅管理服务
@MainActor
final class EasyListService: ObservableObject {
    static let shared = EasyListService()
    
    // MARK: - 发布属性
    @Published var subscriptions: [EasyListSubscription] = []
    @Published var isUpdating: Bool = false
    @Published var compiledRuleSet = CompiledRuleSet()
    @Published var statistics = EasyListStatistics()
    @Published var lastError: String?
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.browser", category: "EasyListService")
    private let userDefaults = UserDefaults.standard
    private let parser = EasyListParser()
    private let fileManager = FileManager.default
    private var updateTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 配置键
    private enum Keys {
        static let subscriptions = "easylist_subscriptions"
        static let statistics = "easylist_statistics"
        static let lastCompilation = "easylist_last_compilation"
    }
    
    // MARK: - 文件路径
    private var documentsDirectory: URL {
        fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
    }
    
    private var easyListDirectory: URL {
        documentsDirectory.appendingPathComponent("EasyList", isDirectory: true)
    }
    
    private var compiledRulesFile: URL {
        easyListDirectory.appendingPathComponent("compiled_rules.json")
    }
    
    private init() {
        createDirectoryIfNeeded()
        loadSubscriptions()
        loadCompiledRules()
        setupAutoUpdate()
        logger.info("🛡️ EasyListService 初始化完成")
    }
    
    deinit {
        updateTimer?.invalidate()
    }
    
    // MARK: - 公共接口
    
    /// 添加默认订阅
    func addDefaultSubscriptions() {
        for defaultSub in DefaultEasyListSubscriptions.all {
            if !subscriptions.contains(where: { $0.url == defaultSub.url }) {
                subscriptions.append(defaultSub)
            }
        }
        saveSubscriptions()
        logger.info("✅ 已添加默认订阅")
    }
    
    /// 添加自定义订阅
    func addSubscription(name: String, url: URL, description: String) {
        let subscription = EasyListSubscription(
            name: name,
            url: url,
            description: description
        )
        subscriptions.append(subscription)
        saveSubscriptions()
        logger.info("✅ 已添加订阅: \(name)")
    }
    
    /// 移除订阅
    func removeSubscription(_ subscription: EasyListSubscription) {
        subscriptions.removeAll { $0.id == subscription.id }
        saveSubscriptions()
        
        // 删除相关文件
        let subscriptionFile = easyListDirectory.appendingPathComponent("\(subscription.id.uuidString).txt")
        try? fileManager.removeItem(at: subscriptionFile)
        
        logger.info("❌ 已移除订阅: \(subscription.name)")
    }
    
    /// 启用/禁用订阅
    func toggleSubscription(_ subscription: EasyListSubscription) {
        if let index = subscriptions.firstIndex(where: { $0.id == subscription.id }) {
            subscriptions[index].isEnabled.toggle()
            saveSubscriptions()
            
            // 重新编译规则
            Task {
                await compileAllRules()
            }
        }
    }
    
    /// 更新所有订阅
    func updateAllSubscriptions() async {
        guard !isUpdating else { return }
        
        isUpdating = true
        lastError = nil
        
        logger.info("🔄 开始更新所有订阅")
        
        for subscription in subscriptions.filter({ $0.isEnabled }) {
            await updateSubscription(subscription)
        }
        
        await compileAllRules()
        updateStatistics()
        
        isUpdating = false
        logger.info("✅ 订阅更新完成")
    }
    
    /// 更新单个订阅
    func updateSubscription(_ subscription: EasyListSubscription) async {
        guard let index = subscriptions.firstIndex(where: { $0.id == subscription.id }) else { return }
        
        subscriptions[index].lastUpdateAttempt = Date()
        subscriptions[index].errorMessage = nil
        
        do {
            logger.info("📥 下载订阅: \(subscription.name)")
            
            // 创建具有更长超时时间的URL请求
            var request = URLRequest(url: subscription.url)
            request.timeoutInterval = 60.0 // 增加到60秒
            request.cachePolicy = .reloadIgnoringLocalCacheData
            
            // 添加适当的请求头
            request.setValue("Mozilla/5.0 (iPad; CPU OS 18_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Mobile/15E148 Safari/604.1", forHTTPHeaderField: "User-Agent")
            request.setValue("text/plain,*/*;q=0.8", forHTTPHeaderField: "Accept")
            
            // 使用自定义session配置
            let configuration = URLSessionConfiguration.default
            configuration.timeoutIntervalForRequest = 60.0
            configuration.timeoutIntervalForResource = 120.0
            configuration.waitsForConnectivity = true
            
            let session = URLSession(configuration: configuration)
            
            let (data, response) = try await session.data(for: request)
            
            // 检查响应状态
            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode != 200 {
                throw EasyListError.downloadFailed
            }
            
            guard let content = String(data: data, encoding: .utf8) else {
                throw EasyListError.invalidContent
            }
            
            // 验证内容不为空
            guard !content.isEmpty else {
                throw EasyListError.invalidContent
            }
            
            // 保存原始内容
            let subscriptionFile = easyListDirectory.appendingPathComponent("\(subscription.id.uuidString).txt")
            try content.write(to: subscriptionFile, atomically: true, encoding: .utf8)
            
            // 解析规则
            let rules = parser.parseFilterList(content, subscriptionId: subscription.id)
            
            // 更新订阅信息
            subscriptions[index].lastUpdated = Date()
            subscriptions[index].ruleCount = rules.count
            subscriptions[index].errorMessage = nil
            
            saveSubscriptions()
            logger.info("✅ 订阅更新成功: \(subscription.name) (\(rules.count) 条规则)")
            
        } catch {
            subscriptions[index].errorMessage = error.localizedDescription
            saveSubscriptions()
            logger.error("❌ 订阅更新失败: \(subscription.name) - \(error.localizedDescription)")
        }
    }
    
    /// 编译所有规则
    func compileAllRules() async {
        logger.info("🔧 开始编译规则")
        let startTime = Date()
        
        var allRules: [CompiledFilterRule] = []
        
        // 加载所有启用订阅的规则
        for subscription in subscriptions.filter({ $0.isEnabled && $0.lastUpdated != nil }) {
            let subscriptionFile = easyListDirectory.appendingPathComponent("\(subscription.id.uuidString).txt")
            
            if fileManager.fileExists(atPath: subscriptionFile.path),
               let content = try? String(contentsOf: subscriptionFile, encoding: .utf8) {
                let rules = parser.parseFilterList(content, subscriptionId: subscription.id)
                allRules.append(contentsOf: rules)
            }
        }
        
        // 优化规则
        allRules = parser.optimizeRules(allRules)
        
        // 编译规则集
        var newRuleSet = CompiledRuleSet()
        for rule in allRules {
            if parser.validateRule(rule) {
                newRuleSet.addRule(rule)
            }
        }
        
        compiledRuleSet = newRuleSet
        
        // 保存编译后的规则
        saveCompiledRules()
        
        let compilationTime = Date().timeIntervalSince(startTime)
        statistics.lastCompilationTime = Date()
        statistics.compilationDuration = compilationTime
        
        logger.info("✅ 规则编译完成: \(allRules.count) 条规则，耗时 \(String(format: "%.2f", compilationTime))s")
    }
    
    /// 获取特定域名的规则
    func getRules(for domain: String) -> [CompiledFilterRule] {
        return compiledRuleSet.getRules(for: domain)
    }
    
    /// 强制更新需要更新的订阅
    func updateOutdatedSubscriptions() async {
        let outdatedSubscriptions = subscriptions.filter { $0.needsUpdate }
        
        if !outdatedSubscriptions.isEmpty {
            logger.info("🔄 更新过期订阅: \(outdatedSubscriptions.count) 个")
            
            for subscription in outdatedSubscriptions {
                await updateSubscription(subscription)
            }
            
            await compileAllRules()
            updateStatistics()
        }
    }
}

// MARK: - 私有方法
private extension EasyListService {
    
    /// 创建目录
    func createDirectoryIfNeeded() {
        if !fileManager.fileExists(atPath: easyListDirectory.path) {
            try? fileManager.createDirectory(at: easyListDirectory, withIntermediateDirectories: true)
        }
    }
    
    /// 加载订阅
    func loadSubscriptions() {
        if let data = userDefaults.data(forKey: Keys.subscriptions),
           let loadedSubscriptions = try? JSONDecoder().decode([EasyListSubscription].self, from: data) {
            subscriptions = loadedSubscriptions
        } else {
            // 首次启动，添加默认订阅
            addDefaultSubscriptions()
        }
    }
    
    /// 保存订阅
    func saveSubscriptions() {
        if let data = try? JSONEncoder().encode(subscriptions) {
            userDefaults.set(data, forKey: Keys.subscriptions)
        }
    }
    
    /// 加载编译后的规则
    func loadCompiledRules() {
        if fileManager.fileExists(atPath: compiledRulesFile.path),
           let data = try? Data(contentsOf: compiledRulesFile),
           let ruleSet = try? JSONDecoder().decode(CompiledRuleSet.self, from: data) {
            compiledRuleSet = ruleSet
        }
    }
    
    /// 保存编译后的规则
    func saveCompiledRules() {
        if let data = try? JSONEncoder().encode(compiledRuleSet) {
            try? data.write(to: compiledRulesFile)
        }
    }
    
    /// 设置自动更新
    func setupAutoUpdate() {
        // 每小时检查一次是否需要更新
        updateTimer = Timer.scheduledTimer(withTimeInterval: 3600, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.updateOutdatedSubscriptions()
            }
        }
    }
    
    /// 更新统计信息
    func updateStatistics() {
        statistics.totalSubscriptions = subscriptions.count
        statistics.enabledSubscriptions = subscriptions.filter { $0.isEnabled }.count
        statistics.totalRules = compiledRuleSet.totalRuleCount
        statistics.networkBlockingRules = compiledRuleSet.networkBlockingRules.count
        statistics.elementHidingRules = compiledRuleSet.elementHidingRules.count
        statistics.exceptionRules = compiledRuleSet.exceptionRules.count
        
        if let data = try? JSONEncoder().encode(statistics) {
            userDefaults.set(data, forKey: Keys.statistics)
        }
    }
}

// MARK: - 错误定义

enum EasyListError: LocalizedError {
    case invalidContent
    case downloadFailed
    case compilationFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidContent:
            return "无效的订阅内容"
        case .downloadFailed:
            return "下载失败"
        case .compilationFailed:
            return "规则编译失败"
        }
    }
}
