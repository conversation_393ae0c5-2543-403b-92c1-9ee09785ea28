//
//  AdBlockService.swift
//  cop
//
//  Created by Augment Agent on 2025/6/16.
//

import Foundation
import WebKit
import OSLog

/// 轻量级广告屏蔽服务
@MainActor
final class AdBlockService: ObservableObject {
    static let shared = AdBlockService()

    // MARK: - 发布属性
    @Published var isEnabled: Bool = true
    @Published var statistics = AdBlockStatistics()
    @Published var customRules: [String] = []
    @Published var whitelist: Set<String> = []
    @Published var easyListEnabled: Bool = true

    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.browser", category: "AdBlockService")
    private let userDefaults = UserDefaults.standard
    private let easyListService = EasyListService.shared
    private let ruleCompiler = FilterRuleCompiler()
    
    // MARK: - 配置键
    private enum Keys {
        static let isEnabled = "adblock_enabled"
        static let customRules = "adblock_custom_rules"
        static let whitelist = "adblock_whitelist"
        static let statistics = "adblock_statistics"
        static let easyListEnabled = "adblock_easylist_enabled"
    }
    
    private init() {
        loadSettings()
        logger.info("🛡️ AdBlockService 初始化完成")
    }
    
    // MARK: - 公共接口
    
    /// 获取广告屏蔽用户脚本
    func createUserScript() -> WKUserScript {
        let scriptSource = generateAdBlockScript()
        return WKUserScript(
            source: scriptSource,
            injectionTime: .atDocumentStart,
            forMainFrameOnly: false
        )
    }

    /// 获取针对特定域名优化的用户脚本
    func createUserScript(for domain: String) -> WKUserScript {
        let scriptSource = generateOptimizedAdBlockScript(for: domain)
        return WKUserScript(
            source: scriptSource,
            injectionTime: .atDocumentStart,
            forMainFrameOnly: false
        )
    }
    
    /// 检查URL是否在白名单中
    func isWhitelisted(_ url: URL) -> Bool {
        guard let host = url.host else { return false }
        return whitelist.contains(host) || whitelist.contains(where: { host.contains($0) })
    }
    
    /// 添加到白名单
    func addToWhitelist(_ host: String) {
        whitelist.insert(host)
        saveSettings()
        logger.info("✅ 已添加到白名单: \(host)")
    }
    
    /// 从白名单移除
    func removeFromWhitelist(_ host: String) {
        whitelist.remove(host)
        saveSettings()
        logger.info("❌ 已从白名单移除: \(host)")
    }
    
    /// 添加自定义规则
    func addCustomRule(_ rule: String) {
        guard !rule.isEmpty && !customRules.contains(rule) else { return }
        customRules.append(rule)
        saveSettings()
        logger.info("📝 已添加自定义规则: \(rule)")
    }
    
    /// 移除自定义规则
    func removeCustomRule(_ rule: String) {
        customRules.removeAll { $0 == rule }
        saveSettings()
        logger.info("🗑️ 已移除自定义规则: \(rule)")
    }
    
    /// 更新统计信息
    func updateStatistics(blocked: Int = 0, allowed: Int = 0) {
        statistics.totalBlocked += blocked
        statistics.totalAllowed += allowed
        statistics.lastUpdated = Date()
        saveStatistics()
        logger.info("📊 广告屏蔽统计更新: 已屏蔽 \(self.statistics.totalBlocked), 已允许 \(self.statistics.totalAllowed)")
    }
    
    /// 处理来自JavaScript的统计消息
    func handleJavaScriptStats(blocked: Int) {
        updateStatistics(blocked: blocked)
    }

    /// 处理来自EasyList的统计消息
    func handleEasyListStats(blocked: Int) {
        updateStatistics(blocked: blocked)
    }

    /// 重置统计信息
    func resetStatistics() {
        statistics = AdBlockStatistics()
        saveStatistics()
        logger.info("📊 统计信息已重置")
    }

    /// 启用/禁用EasyList
    func toggleEasyList() {
        easyListEnabled.toggle()
        saveSettings()
        logger.info("🛡️ EasyList \(self.easyListEnabled ? "已启用" : "已禁用")")
    }
}

// MARK: - 广告屏蔽统计
struct AdBlockStatistics: Codable {
    var totalBlocked: Int = 0
    var totalAllowed: Int = 0
    var lastUpdated: Date = Date()
    
    var blockingRate: Double {
        let total = totalBlocked + totalAllowed
        return total > 0 ? Double(totalBlocked) / Double(total) : 0.0
    }
}

// MARK: - 私有方法
private extension AdBlockService {
    
    /// 生成广告屏蔽JavaScript脚本
    func generateAdBlockScript() -> String {
        guard isEnabled else {
            return "// AdBlock disabled"
        }

        let baseRules = getBaseAdBlockRules()
        let customRulesJS = customRules.map { "'\($0)'" }.joined(separator: ",")
        
        return """
        (function() {
            'use strict';
            
            // 广告屏蔽配置
            const adBlockConfig = {
                enabled: true,
                baseRules: [\(baseRules.map { "'\($0)'" }.joined(separator: ","))],
                customRules: [\(customRulesJS)],
                blockedCount: 0
            };
            
            // 广告域名黑名单
            const adDomains = [
                'doubleclick.net',
                'googleadservices.com',
                'googlesyndication.com',
                'googletagmanager.com',
                'facebook.com/tr',
                'amazon-adsystem.com',
                'adsystem.amazon.com',
                'ads.yahoo.com',
                'advertising.com',
                'adsystem.com'
            ];
            
            // CSS选择器规则
            const adSelectors = [
                '[class*="ad-"]',
                '[class*="ads-"]',
                '[id*="ad-"]',
                '[id*="ads-"]',
                '.advertisement',
                '.ad-banner',
                '.ad-container',
                '.sponsored',
                '[data-ad]',
                'iframe[src*="ads"]'
            ].concat(adBlockConfig.baseRules).concat(adBlockConfig.customRules);
            
            // 隐藏广告元素
            function hideAdElements() {
                try {
                    adSelectors.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(element => {
                            if (element && element.style.display !== 'none') {
                                element.style.display = 'none';
                                element.style.visibility = 'hidden';
                                element.style.opacity = '0';
                                element.style.height = '0';
                                element.style.width = '0';
                                adBlockConfig.blockedCount++;
                            }
                        });
                    });
                } catch (error) {
                    console.log('AdBlock: 元素隐藏出错', error);
                }
            }
            
            // 拦截网络请求
            function interceptRequests() {
                // 拦截fetch请求
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    const url = args[0];
                    if (typeof url === 'string' && shouldBlockUrl(url)) {
                        adBlockConfig.blockedCount++;
                        return Promise.reject(new Error('Blocked by AdBlock'));
                    }
                    return originalFetch.apply(this, args);
                };
                
                // 拦截XMLHttpRequest
                const originalOpen = XMLHttpRequest.prototype.open;
                XMLHttpRequest.prototype.open = function(method, url, ...args) {
                    if (shouldBlockUrl(url)) {
                        adBlockConfig.blockedCount++;
                        throw new Error('Blocked by AdBlock');
                    }
                    return originalOpen.apply(this, [method, url, ...args]);
                };
            }
            
            // 检查URL是否应该被屏蔽
            function shouldBlockUrl(url) {
                if (!url || typeof url !== 'string') return false;
                
                return adDomains.some(domain => url.includes(domain)) ||
                       url.includes('/ads/') ||
                       url.includes('advertisement') ||
                       url.includes('sponsored');
            }
            
            // 观察DOM变化
            function observeDOM() {
                const observer = new MutationObserver(function(mutations) {
                    let shouldCheck = false;
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                            shouldCheck = true;
                        }
                    });
                    
                    if (shouldCheck) {
                        setTimeout(hideAdElements, 100);
                    }
                });
                
                observer.observe(document.body || document.documentElement, {
                    childList: true,
                    subtree: true
                });
            }
            
            // 初始化广告屏蔽
            function initAdBlock() {
                hideAdElements();
                interceptRequests();
                
                if (document.body) {
                    observeDOM();
                } else {
                    document.addEventListener('DOMContentLoaded', observeDOM);
                }
                
                // 定期检查新的广告元素
                setInterval(hideAdElements, 2000);
                
                console.log('🛡️ AdBlock 已激活');
            }
            
            // 启动
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initAdBlock);
            } else {
                initAdBlock();
            }
            
            // 向原生应用报告统计信息
            function reportStats() {
                if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.adBlockStats) {
                    window.webkit.messageHandlers.adBlockStats.postMessage({
                        blocked: adBlockConfig.blockedCount,
                        timestamp: Date.now()
                    });
                    console.log('🛡️ AdBlock 统计报告: 已屏蔽', adBlockConfig.blockedCount);
                }
            }
            
            // 立即报告一次
            reportStats();
            
            // 每3秒报告一次
            setInterval(reportStats, 3000);
            
            // 在屏蔽广告时也立即报告
            const originalBlockedCount = adBlockConfig.blockedCount;
            const checkAndReport = () => {
                if (adBlockConfig.blockedCount > originalBlockedCount) {
                    reportStats();
                }
            };
            
            // 添加到所有屏蔽操作后
            setInterval(checkAndReport, 1000);
            
        })();
        """
    }

    /// 生成针对特定域名优化的广告屏蔽脚本
    func generateOptimizedAdBlockScript(for domain: String) -> String {
        guard isEnabled else {
            return "// AdBlock disabled"
        }

        // 基础脚本（自定义规则和基础规则）
        var combinedScript = generateAdBlockScript()

        // 如果启用了EasyList，添加EasyList规则
        if easyListEnabled {
            let easyListRules = easyListService.getRules(for: domain)
            let networkRules = ruleCompiler.compileNetworkRules(easyListRules)
            let elementRules = ruleCompiler.compileElementRules(easyListRules)

            let easyListScript = ruleCompiler.generateOptimizedJavaScript(
                networkRules: networkRules,
                elementRules: elementRules,
                domain: domain
            )

            // 合并脚本
            combinedScript = """
            \(combinedScript)

            \(easyListScript)
            """
        }

        return combinedScript
    }
    
    /// 获取基础广告屏蔽规则
    func getBaseAdBlockRules() -> [String] {
        return [
            ".ad",
            ".ads",
            ".advert",
            ".advertisement",
            ".ad-banner",
            ".ad-container",
            ".ad-wrapper",
            ".sponsored-content",
            ".promotion",
            "[data-ad-slot]",
            "[data-google-ad]",
            "ins.adsbygoogle"
        ]
    }

    /// 加载设置
    func loadSettings() {
        // 默认启用广告屏蔽，除非用户明确禁用
        if userDefaults.object(forKey: Keys.isEnabled) == nil {
            isEnabled = true
            userDefaults.set(true, forKey: Keys.isEnabled)
        } else {
            isEnabled = userDefaults.bool(forKey: Keys.isEnabled)
        }

        // 默认启用EasyList，除非用户明确禁用
        if userDefaults.object(forKey: Keys.easyListEnabled) == nil {
            easyListEnabled = true
            userDefaults.set(true, forKey: Keys.easyListEnabled)
        } else {
            easyListEnabled = userDefaults.bool(forKey: Keys.easyListEnabled)
        }

        customRules = userDefaults.stringArray(forKey: Keys.customRules) ?? []

        if let whitelistArray = userDefaults.stringArray(forKey: Keys.whitelist) {
            whitelist = Set(whitelistArray)
        }

        if let statisticsData = userDefaults.data(forKey: Keys.statistics),
           let loadedStats = try? JSONDecoder().decode(AdBlockStatistics.self, from: statisticsData) {
            statistics = loadedStats
            logger.info("📊 已加载广告屏蔽统计: 已屏蔽 \(self.statistics.totalBlocked), 已允许 \(self.statistics.totalAllowed)")
        } else {
            // 如果没有保存的统计数据，创建新的
            statistics = AdBlockStatistics()
            saveStatistics()
            logger.info("📊 创建新的广告屏蔽统计")
        }
    }

    /// 保存设置
    func saveSettings() {
        userDefaults.set(isEnabled, forKey: Keys.isEnabled)
        userDefaults.set(easyListEnabled, forKey: Keys.easyListEnabled)
        userDefaults.set(customRules, forKey: Keys.customRules)
        userDefaults.set(Array(whitelist), forKey: Keys.whitelist)
    }

    /// 保存统计信息
    func saveStatistics() {
        if let statisticsData = try? JSONEncoder().encode(statistics) {
            userDefaults.set(statisticsData, forKey: Keys.statistics)
        }
    }
}
