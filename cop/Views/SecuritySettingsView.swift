//
//  SecuritySettingsView.swift
//  cop
//
//  Created by Augment Agent on 2025/6/2.
//

import SwiftUI

// MARK: - 安全设置主视图
// 使用统一的BrowserNavigationDestination枚举
struct SecuritySettingsView: View {
    @StateObject private var securityService = SecurityService.shared
    @Environment(\.dismiss) private var dismiss
    
    // 接收父级的导航路径
    @Binding var navigationPath: NavigationPath
    
    var body: some View {
        List {
            // 网络安全
            networkSecuritySection
            
            // 数据保护
            dataProtectionSection
            
            // 隐私保护
            privacyProtectionSection
            
            // 权限管理
            permissionManagementSection
            
            // 统计信息
            statisticsSection
            
            // 高级设置
            advancedSettingsSection
        }
        .navigationTitle("安全与隐私")
        .navigationBarTitleDisplayMode(.large)
    }
    
    // MARK: - 网络安全区域
    private var networkSecuritySection: some View {
        Section {
            SecurityToggleRow(
                icon: "lock.shield",
                iconColor: .green,
                title: "HTTPS 强制",
                subtitle: "自动将 HTTP 连接升级为 HTTPS",
                isOn: $securityService.settings.isHTTPSOnlyEnabled
            )
            
            SecurityToggleRow(
                icon: "checkmark.shield",
                iconColor: .blue,
                title: "证书验证",
                subtitle: "验证网站 SSL/TLS 证书的有效性",
                isOn: $securityService.settings.isCertificateValidationEnabled
            )
            
            if securityService.settings.certificateValidation {
                SecurityToggleRow(
                    icon: "checkmark.shield.fill",
                    iconColor: .purple,
                    title: "严格证书验证",
                    subtitle: "启用证书透明度和更严格的验证",
                    isOn: $securityService.settings.strictCertificateValidation
                )
            }
            
            SecurityToggleRow(
                icon: "exclamationmark.shield",
                iconColor: .orange,
                title: "欺诈网站警告",
                subtitle: "警告访问已知的恶意或钓鱼网站",
                isOn: $securityService.settings.isFraudulentWebsiteWarningEnabled
            )
            
            SecurityToggleRow(
                icon: "shield.checkered",
                iconColor: .red,
                title: "高级威胁检测",
                subtitle: "使用机器学习检测新型威胁",
                isOn: $securityService.settings.advancedThreatDetection
            )
            
            SecurityToggleRow(
                icon: "exclamationmark.triangle",
                iconColor: .yellow,
                title: "混合内容警告",
                subtitle: "警告 HTTPS 页面加载 HTTP 资源",
                isOn: $securityService.settings.isMixedContentWarningEnabled
            )
            
            SecurityToggleRow(
                icon: "arrow.up.right.square",
                iconColor: .purple,
                title: "HSTS 支持",
                subtitle: "支持 HTTP 严格传输安全",
                isOn: $securityService.settings.hstsEnabled
            )
        } header: {
            Text("网络安全")
        } footer: {
            Text("这些设置有助于保护您免受网络攻击和不安全连接的威胁。高级威胁检测使用最新的安全算法。")
        }
    }
    
    // MARK: - 数据保护区域
    private var dataProtectionSection: some View {
        Section {
            SecurityToggleRow(
                icon: "key.fill",
                iconColor: .blue,
                title: "数据加密",
                subtitle: "加密存储敏感的本地数据",
                isOn: $securityService.settings.dataEncryptionEnabled
            )
            
            if securityService.settings.dataEncryption {
                HStack {
                    Image(systemName: "lock.rotation")
                        .foregroundColor(.blue)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("加密级别")
                            .font(.body)
                        Text(securityService.settings.encryptionLevel.displayName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Picker("", selection: $securityService.settings.encryptionLevel) {
                        ForEach(EncryptionLevel.allCases, id: \.self) { level in
                            Text(level.displayName).tag(level)
                        }
                    }
                    .pickerStyle(.menu)
                }
            }
            
            SecurityToggleRow(
                icon: "clock.arrow.circlepath",
                iconColor: .orange,
                title: "自动数据过期",
                subtitle: "自动清理过期的浏览数据",
                isOn: $securityService.settings.autoDataExpirationEnabled
            )
            
            if securityService.settings.autoDataExpiration {
                HStack {
                    Image(systemName: "calendar")
                        .foregroundColor(.gray)
                        .frame(width: 24)
                    
                    Text("数据保留天数")
                    
                    Spacer()
                    
                    Picker("", selection: $securityService.settings.dataExpirationDays) {
                        Text("7 天").tag(7)
                        Text("30 天").tag(30)
                        Text("90 天").tag(90)
                        Text("365 天").tag(365)
                    }
                    .pickerStyle(.menu)
                }
            }
            
            SecurityToggleRow(
                icon: "square.split.2x2",
                iconColor: .green,
                title: "数据隔离",
                subtitle: "隔离不同会话的数据",
                isOn: $securityService.settings.dataIsolationEnabled
            )
        } header: {
            Text("数据保护")
        } footer: {
            VStack(alignment: .leading, spacing: 4) {
                Text("保护您的本地数据免受未经授权的访问。")
                if securityService.settings.dataEncryption {
                    Text("当前加密级别：\(securityService.settings.encryptionLevel.description)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    // MARK: - 隐私保护区域
    private var privacyProtectionSection: some View {
        Section {
            HStack {
                Image(systemName: "doc.text")
                    .foregroundColor(.brown)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Cookie 策略")
                        .font(.body)
                    Text(securityService.settings.cookiePolicy.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Picker("", selection: $securityService.settings.cookiePolicy) {
                    ForEach(CookiePolicy.allCases, id: \.self) { policy in
                        Text(policy.displayName).tag(policy)
                    }
                }
                .pickerStyle(.menu)
            }
            
            HStack {
                Image(systemName: "eye.slash.fill")
                    .foregroundColor(.indigo)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("追踪保护")
                        .font(.body)
                    Text(securityService.settings.trackingProtection.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Picker("", selection: $securityService.settings.trackingProtection) {
                    ForEach(TrackingProtectionLevel.allCases, id: \.self) { level in
                        Text(level.displayName).tag(level)
                    }
                }
                .pickerStyle(.menu)
            }
            
            SecurityToggleRow(
                icon: "curlybraces",
                iconColor: .orange,
                title: "JavaScript",
                subtitle: "允许网站运行 JavaScript 代码",
                isOn: $securityService.settings.javascriptEnabled
            )
            
            SecurityToggleRow(
                icon: "hand.raised.fill",
                iconColor: .red,
                title: "指纹识别防护",
                subtitle: "阻止网站收集设备指纹信息",
                isOn: $securityService.settings.fingerprinting
            )
            
            HStack {
                Image(systemName: "arrow.turn.up.right")
                    .foregroundColor(.blue)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Referer 策略")
                        .font(.body)
                    Text(securityService.settings.refererPolicy.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Picker("", selection: $securityService.settings.refererPolicy) {
                    ForEach(RefererPolicy.allCases, id: \.self) { policy in
                        Text(policy.displayName).tag(policy)
                    }
                }
                .pickerStyle(.menu)
            }
            
            SecurityToggleRow(
                icon: "video.slash",
                iconColor: .red,
                title: "禁用 WebRTC",
                subtitle: "防止 WebRTC 泄露真实 IP 地址",
                isOn: .constant(!securityService.settings.webRTCEnabled)
            )
        } header: {
            Text("隐私保护")
        } footer: {
            VStack(alignment: .leading, spacing: 4) {
                Text("管理您的隐私偏好，例如 Cookie 和 JavaScript 的处理方式。")
                Text("当前追踪保护：\(securityService.settings.trackingProtection.description)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - 权限管理区域
    private var permissionManagementSection: some View {
        Section {
            Button(action: { 
                navigationPath.append(BrowserNavigationDestination.permissions)
            }) {
                HStack {
                    Image(systemName: "hand.raised.fill")
                        .foregroundColor(.blue)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("网站权限")
                            .foregroundColor(.primary)
                        Text("\(securityService.sitePermissions.count) 个权限记录")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                }
            }
            
            SecurityToggleRow(
                icon: "lock.shield",
                iconColor: .red,
                title: "严格权限模式",
                subtitle: "默认拒绝所有权限请求",
                isOn: $securityService.settings.strictPermissionMode
            )
        } header: {
            Text("权限管理")
        } footer: {
            Text("管理网站对设备功能的访问权限。")
        }
    }
    
    // MARK: - 统计信息区域
    private var statisticsSection: some View {
        Section {
            Button(action: { 
                navigationPath.append(BrowserNavigationDestination.statistics)
            }) {
                HStack {
                    Image(systemName: "chart.bar.fill")
                        .foregroundColor(.green)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("安全统计")
                            .foregroundColor(.primary)
                        Text("查看详细的安全保护统计")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("\(securityService.statistics.httpsUpgrades)")
                            .font(.headline)
                            .foregroundColor(.green)
                        Text("HTTPS 升级")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                }
            }
        } header: {
            Text("统计信息")
        }
    }
    
    // MARK: - 高级设置区域
    private var advancedSettingsSection: some View {
        Section {
            Button("重置安全统计") {
                securityService.statistics.reset()
            }
            .foregroundColor(.red)
        } header: {
            Text("高级设置")
        } footer: {
            Text("重置安全保护统计数据。")
        }
    }
}

// MARK: - 安全开关行组件
struct SecurityToggleRow: View {
    let icon: String
    let iconColor: Color
    let title: String
    let subtitle: String
    @Binding var isOn: Bool
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(iconColor)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.body)
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
        }
        .padding(.vertical, 2)
    }
}

// MARK: - 预览
struct SecuritySettingsView_Previews: PreviewProvider {
    static var previews: some View {
        SecuritySettingsView(navigationPath: .constant(NavigationPath()))
    }
}
