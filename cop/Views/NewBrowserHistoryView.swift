//
//  NewBrowserHistoryView.swift
//  cop
//
//  Created by Augment Agent on 2025/6/2.
//

import SwiftUI

// MARK: - 新浏览器历史记录视图
struct NewBrowserHistoryView: View {
    @ObservedObject var browserViewModel: NewWebBrowserViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    @State private var selectedDateFilter: HistoryDateFilter = .all
    
    // 日期筛选选项
    enum HistoryDateFilter: String, CaseIterable {
        case all = "全部"
        case today = "今天"
        case yesterday = "昨天"
        case lastWeek = "最近一周"
        case lastMonth = "最近一个月"
        
        var dateRange: DateInterval? {
            let calendar = Calendar.current
            let now = Date()
            
            switch self {
            case .all:
                return nil
            case .today:
                let startOfDay = calendar.startOfDay(for: now)
                return DateInterval(start: startOfDay, end: now)
            case .yesterday:
                guard let yesterday = calendar.date(byAdding: .day, value: -1, to: now),
                      let startOfYesterday = calendar.dateInterval(of: .day, for: yesterday) else {
                    return nil
                }
                return startOfYesterday
            case .lastWeek:
                guard let weekAgo = calendar.date(byAdding: .weekOfYear, value: -1, to: now) else {
                    return nil
                }
                return DateInterval(start: weekAgo, end: now)
            case .lastMonth:
                guard let monthAgo = calendar.date(byAdding: .month, value: -1, to: now) else {
                    return nil
                }
                return DateInterval(start: monthAgo, end: now)
            }
        }
    }
    
    var filteredHistory: [HistoryItem] {
        var history = browserViewModel.history
        
        // 首先按访问时间倒序排列（最新访问的在前）
        history = history.sorted { $0.visitDate > $1.visitDate }
        
        // 应用日期筛选
        if let dateRange = selectedDateFilter.dateRange {
            history = history.filter { dateRange.contains($0.visitDate) }
        }
        
        // 应用搜索筛选
        if !searchText.isEmpty {
            history = history.filter { item in
                item.title.localizedCaseInsensitiveContains(searchText) ||
                item.url.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        return history
    }
    
    // 按日期分组的历史记录
    var groupedHistory: [(String, [HistoryItem])] {
        let calendar = Calendar.current
        let grouped = Dictionary(grouping: filteredHistory) { item in
            calendar.startOfDay(for: item.visitDate)
        }
        
        return grouped.map { (date, items) in
            let formatter = DateFormatter()
            if calendar.isDateInToday(date) {
                return ("今天", items.sorted { $0.visitDate > $1.visitDate })
            } else if calendar.isDateInYesterday(date) {
                return ("昨天", items.sorted { $0.visitDate > $1.visitDate })
            } else if calendar.isDate(date, equalTo: Date(), toGranularity: .weekOfYear) {
                formatter.dateFormat = "EEEE"
                return (formatter.string(from: date), items.sorted { $0.visitDate > $1.visitDate })
            } else {
                formatter.dateFormat = "MM月dd日"
                return (formatter.string(from: date), items.sorted { $0.visitDate > $1.visitDate })
            }
        }.sorted { first, second in
            // 根据第一个项目的日期排序分组
            let firstDate = first.1.first?.visitDate ?? Date.distantPast
            let secondDate = second.1.first?.visitDate ?? Date.distantPast
            return firstDate > secondDate
        }
    }
    
    var body: some View {
        VStack {
            if browserViewModel.history.isEmpty {
                // 空状态
                VStack(spacing: 16) {
                    Spacer()
                    
                    Image(systemName: "clock")
                        .font(.system(size: 48))
                        .foregroundColor(.gray)
                    
                    Text("没有浏览历史")
                        .font(.title2)
                        .foregroundColor(.gray)
                    
                    Text("您访问的网页将显示在这里")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    
                    Spacer()
                }
            } else {
                VStack(spacing: 0) {
                    // 日期筛选器
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(HistoryDateFilter.allCases, id: \.rawValue) { filter in
                                Button(action: {
                                    selectedDateFilter = filter
                                }) {
                                    Text(filter.rawValue)
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(selectedDateFilter == filter ? .white : .primary)
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(
                                            RoundedRectangle(cornerRadius: 20)
                                                .fill(selectedDateFilter == filter ? Color.blue : Color(UIColor.systemGray5))
                                        )
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                        .padding(.horizontal, 16)
                    }
                    .padding(.vertical, 8)
                    .background(Color(UIColor.systemBackground))
                    
                    Divider()
                    
                    // 历史记录列表
                    List {
                        ForEach(groupedHistory, id: \.0) { date, items in
                            Section(date) {
                                ForEach(items) { item in
                                    NewHistoryItemRow(
                                        item: item,
                                        onTap: {
                                            print("🔗 点击历史记录: \(item.title) - \(item.url)")
                                            if let url = URL(string: item.url) {
                                                print("✅ 历史记录URL有效，开始加载: \(url)")
                                                
                                                // 关闭弹窗
                                                dismiss()
                                                
                                                // 延迟加载以确保UI更新完成
                                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                                    browserViewModel.loadURL(url)
                                                    // 刷新UI
                                                    browserViewModel.refreshCurrentTab()
                                                }
                                            } else {
                                                print("❌ 历史记录URL无效: \(item.url)")
                                            }
                                        }
                                    )
                                }
                                .onDelete { indexSet in
                                    // 删除选中的历史记录项
                                    for index in indexSet {
                                        let itemToDelete = items[index]
                                        browserViewModel.history.removeAll { $0.id == itemToDelete.id }
                                    }
                                }
                            }
                        }
                    }
                    .searchable(text: $searchText, prompt: "搜索历史记录")
                }
            }
        }
        .navigationTitle("浏览历史")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Menu {
                    Button(action: {
                        // 清除今天的历史记录
                        let calendar = Calendar.current
                        let today = calendar.startOfDay(for: Date())
                        browserViewModel.history.removeAll { calendar.isDate($0.visitDate, inSameDayAs: today) }
                    }) {
                        Label("清除今天", systemImage: "clock")
                    }
                    
                    Button(action: {
                        // 清除最近一周的历史记录
                        let weekAgo = Calendar.current.date(byAdding: .weekOfYear, value: -1, to: Date()) ?? Date()
                        browserViewModel.history.removeAll { $0.visitDate >= weekAgo }
                    }) {
                        Label("清除最近一周", systemImage: "calendar")
                    }
                    
                    Divider()
                    
                    Button(role: .destructive, action: {
                        browserViewModel.clearHistory()
                    }) {
                        Label("清除全部历史", systemImage: "trash")
                    }
                } label: {
                    Image(systemName: "ellipsis.circle")
                }
                .disabled(browserViewModel.history.isEmpty)
            }
        }
    }
}

// MARK: - 新历史记录项行视图
struct NewHistoryItemRow: View {
    let item: HistoryItem
    let onTap: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 网站图标
            AsyncImage(url: URL(string: "https://www.google.com/s2/favicons?domain=\(URL(string: item.url)?.host ?? "")&sz=32")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fit)
            } placeholder: {
                Image(systemName: "globe")
                    .foregroundColor(.gray)
            }
            .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                // 标题
                Text(item.title.isEmpty ? "无标题" : item.title)
                    .font(.body)
                    .lineLimit(1)
                
                // URL
                Text(item.url)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
                
                // 访问时间
                Text(item.visitDate, style: .relative)
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
            
            Spacer()
        }
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - 书签视图
struct NewBookmarksView: View {
    @ObservedObject var browserViewModel: NewWebBrowserViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    @State private var showingAddBookmark = false
    @State private var newBookmarkTitle = ""
    @State private var newBookmarkURL = ""
    @State private var newBookmarkCategory = "默认"
    
    var filteredBookmarks: [BookmarkItem] {
        let bookmarks: [BookmarkItem]
        if searchText.isEmpty {
            bookmarks = browserViewModel.bookmarks
        } else {
            bookmarks = browserViewModel.bookmarks.filter { bookmark in
                bookmark.title.localizedCaseInsensitiveContains(searchText) ||
                bookmark.url.localizedCaseInsensitiveContains(searchText) ||
                bookmark.category.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // 按添加时间倒序排列（最新添加的在前）
        return bookmarks.sorted { $0.dateAdded > $1.dateAdded }
    }
    
    var groupedBookmarks: [String: [BookmarkItem]] {
        let grouped = Dictionary(grouping: filteredBookmarks) { $0.category }
        
        // 对每个分类内的书签也按时间倒序排列
        var sortedGrouped: [String: [BookmarkItem]] = [:]
        for (category, bookmarks) in grouped {
            sortedGrouped[category] = bookmarks.sorted { $0.dateAdded > $1.dateAdded }
        }
        return sortedGrouped
    }
    
    var body: some View {
        VStack {
            if browserViewModel.bookmarks.isEmpty {
                emptyStateView
            } else {
                bookmarkListView
            }
        }
        .navigationTitle("书签")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            toolbarContent
        }
        .sheet(isPresented: $showingAddBookmark) {
            addBookmarkSheet
        }
    }
    
    // MARK: - Subviews
    
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Spacer()
            
            Image(systemName: "book")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            Text("没有书签")
                .font(.title2)
                .foregroundColor(.gray)
            
            Text("您收藏的网页将显示在这里")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("添加书签") {
                showingAddBookmark = true
            }
            .buttonStyle(.borderedProminent)
            
            Spacer()
        }
    }
    
    private var bookmarkListView: some View {
        List {
            ForEach(groupedBookmarks.keys.sorted(), id: \.self) { category in
                Section(category) {
                    ForEach(groupedBookmarks[category] ?? []) { bookmark in
                        BookmarkItemRow(
                            bookmark: bookmark,
                            onTap: {
                                handleBookmarkTap(bookmark)
                            }
                        )
                    }
                    .onDelete { indexSet in
                        deleteBookmarks(in: category, at: indexSet)
                    }
                }
            }
        }
        .searchable(text: $searchText, prompt: "搜索书签")
    }
    
    @ToolbarContentBuilder
    private var toolbarContent: some ToolbarContent {
        ToolbarItem(placement: .navigationBarLeading) {
            Button("完成") {
                dismiss()
            }
        }
        
        ToolbarItem(placement: .navigationBarTrailing) {
            Button("添加") {
                showingAddBookmark = true
            }
        }
    }
    
    private var addBookmarkSheet: some View {
        AddBookmarkView(
            title: $newBookmarkTitle,
            url: $newBookmarkURL,
            category: $newBookmarkCategory,
            onAdd: {
                browserViewModel.addBookmark(
                    url: newBookmarkURL,
                    title: newBookmarkTitle
                )
                newBookmarkTitle = ""
                newBookmarkURL = ""
                newBookmarkCategory = "默认"
                showingAddBookmark = false
            }
        )
    }
    
    // MARK: - Helper Methods
    
    private func handleBookmarkTap(_ bookmark: BookmarkItem) {
        print("📖 点击书签: \(bookmark.title) - \(bookmark.url)")
        if let url = URL(string: bookmark.url) {
            print("✅ 书签URL有效，开始加载: \(url)")
            
            // 关闭弹窗
            dismiss()
            
            // 延迟加载以确保UI更新完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                browserViewModel.loadURL(url)
                // 刷新UI
                browserViewModel.refreshCurrentTab()
            }
        } else {
            print("❌ 书签URL无效: \(bookmark.url)")
        }
    }
    
    private func deleteBookmarks(in category: String, at indexSet: IndexSet) {
        for index in indexSet {
            let bookmarkToDelete = (groupedBookmarks[category] ?? [])[index]
            browserViewModel.removeBookmark(bookmarkToDelete)
        }
    }
}

// MARK: - 书签项行视图
struct BookmarkItemRow: View {
    let bookmark: BookmarkItem
    let onTap: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 网站图标
            AsyncImage(url: URL(string: "https://www.google.com/s2/favicons?domain=\(URL(string: bookmark.url)?.host ?? "")&sz=32")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fit)
            } placeholder: {
                Image(systemName: "star.fill")
                    .foregroundColor(.yellow)
            }
            .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                // 标题
                Text(bookmark.title)
                    .font(.body)
                    .lineLimit(1)
                
                // URL
                Text(bookmark.url)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
                
                // 创建时间
                Text(bookmark.dateAdded, style: .date)
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
            
            Spacer()
        }
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - 添加书签视图
struct AddBookmarkView: View {
    @Binding var title: String
    @Binding var url: String
    @Binding var category: String
    let onAdd: () -> Void
    @Environment(\.dismiss) private var dismiss
    
    private let categories = ["默认", "工作", "娱乐", "学习", "购物", "新闻"]
    
    var body: some View {
        NavigationView {
            Form {
                Section("书签信息") {
                    TextField("标题", text: $title)
                    TextField("网址", text: $url)
                        .keyboardType(.URL)
                        .autocapitalization(.none)
                }
                
                Section("分类") {
                    Picker("分类", selection: $category) {
                        ForEach(categories, id: \.self) { category in
                            Text(category).tag(category)
                        }
                    }
                    .pickerStyle(.menu)
                }
            }
            .navigationTitle("添加书签")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("添加") {
                        onAdd()
                    }
                    .disabled(title.isEmpty || url.isEmpty)
                }
            }
        }
    }
}

// MARK: - 预览
struct NewBrowserHistoryView_Previews: PreviewProvider {
    static var previews: some View {
        NewBrowserHistoryView(browserViewModel: NewWebBrowserViewModel())
    }
}

struct NewBookmarksView_Previews: PreviewProvider {
    static var previews: some View {
        NewBookmarksView(browserViewModel: NewWebBrowserViewModel())
    }
}
