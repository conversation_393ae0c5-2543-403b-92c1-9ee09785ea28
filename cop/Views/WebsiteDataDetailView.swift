//
//  WebsiteDataDetailView.swift
//  cop
//
//  Created by Enhanced Agent on 2025/1/2.
//

import SwiftUI
import WebKit

// MARK: - 网站数据详情视图
struct WebsiteDataDetailView: View {
    let records: [WKWebsiteDataRecord]
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    @State private var selectedDataType: DataTypeFilter = .all
    @State private var showingClearConfirmation = false
    @State private var recordToDelete: WKWebsiteDataRecord?
    
    enum DataTypeFilter: String, CaseIterable {
        case all = "全部"
        case cookies = "Cookie"
        case cache = "缓存"
        case localStorage = "本地存储"
        case databases = "数据库"
        
        var dataTypes: Set<String> {
            switch self {
            case .all:
                return Set()
            case .cookies:
                return Set([WKWebsiteDataTypeCookies])
            case .cache:
                return Set([WKWebsiteDataTypeDiskCache, WKWebsiteDataTypeMemoryCache])
            case .localStorage:
                return Set([WKWebsiteDataTypeLocalStorage, WKWebsiteDataTypeSessionStorage])
            case .databases:
                return Set([WKWebsiteDataTypeWebSQLDatabases, WKWebsiteDataTypeIndexedDBDatabases])
            }
        }
    }
    
    var filteredRecords: [WKWebsiteDataRecord] {
        var filtered = records
        
        // 按搜索文本过滤
        if !searchText.isEmpty {
            filtered = filtered.filter { record in
                record.displayName.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // 按数据类型过滤
        if selectedDataType != .all {
            filtered = filtered.filter { record in
                !record.dataTypes.intersection(selectedDataType.dataTypes).isEmpty
            }
        }
        
        return filtered.sorted { $0.displayName < $1.displayName }
    }
    
    var body: some View {
        VStack {
            // 搜索和过滤器
            searchAndFilterSection
            
            // 数据统计
            dataStatisticsSection
            
            // 网站数据列表
            websiteDataList
        }
        .navigationTitle("网站数据详情")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("完成") {
                    dismiss()
                }
            }
        }
        .alert("删除网站数据", isPresented: $showingClearConfirmation) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                if let record = recordToDelete {
                    deleteWebsiteData(record)
                }
            }
        } message: {
            if let record = recordToDelete {
                Text("确定要删除 \(record.displayName) 的所有数据吗？这将清除该网站的登录状态和偏好设置。")
            }
        }
    }
    
    // MARK: - 搜索和过滤区域
    private var searchAndFilterSection: some View {
        VStack(spacing: 12) {
            // 搜索框
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("搜索网站", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !searchText.isEmpty {
                    Button("清除") {
                        searchText = ""
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            .padding(12)
            .background(Color(.systemGray6))
            .cornerRadius(10)
            
            // 数据类型过滤器
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(DataTypeFilter.allCases, id: \.self) { filter in
                        DataFilterChip(
                            title: filter.rawValue,
                            isSelected: selectedDataType == filter
                        ) {
                            selectedDataType = filter
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
    }
    
    // MARK: - 数据统计区域
    private var dataStatisticsSection: some View {
        HStack(spacing: 20) {
            StatisticItem(
                title: "网站总数",
                value: "\(records.count)",
                icon: "globe",
                color: .blue
            )
            
            Divider()
                .frame(height: 30)
            
            StatisticItem(
                title: "Cookie网站",
                value: "\(cookieCount)",
                icon: "doc.text",
                color: .orange
            )
            
            Divider()
                .frame(height: 30)
            
            StatisticItem(
                title: "缓存网站",
                value: "\(cacheCount)",
                icon: "externaldrive",
                color: .green
            )
        }
        .padding()
        .background(Color(.systemGray6))
    }
    
    // MARK: - 网站数据列表
    private var websiteDataList: some View {
        List {
            ForEach(filteredRecords, id: \.displayName) { record in
                WebsiteDataRow(record: record) {
                    recordToDelete = record
                    showingClearConfirmation = true
                }
            }
        }
        .listStyle(PlainListStyle())
    }
    
    // MARK: - 计算属性
    private var cookieCount: Int {
        records.filter { $0.dataTypes.contains(WKWebsiteDataTypeCookies) }.count
    }
    
    private var cacheCount: Int {
        records.filter { 
            $0.dataTypes.contains(WKWebsiteDataTypeDiskCache) || 
            $0.dataTypes.contains(WKWebsiteDataTypeMemoryCache) 
        }.count
    }
    
    // MARK: - 数据操作方法
    private func deleteWebsiteData(_ record: WKWebsiteDataRecord) {
        Task {
            await WKWebsiteDataStore.default().removeData(
                ofTypes: record.dataTypes,
                for: [record]
            )
        }
    }
}

// MARK: - 数据过滤器芯片组件
struct DataFilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(isSelected ? Color.blue : Color(.systemGray5))
                )
                .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 统计项目组件
struct StatisticItem: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - 网站数据行组件
struct WebsiteDataRow: View {
    let record: WKWebsiteDataRecord
    let onDelete: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                // 网站图标和名称
                VStack(alignment: .leading, spacing: 2) {
                    Text(record.displayName)
                        .font(.body)
                        .fontWeight(.medium)
                        .lineLimit(1)
                    
                    Text(dataTypesDescription)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                Spacer()
                
                // 删除按钮
                Button(action: onDelete) {
                    Image(systemName: "trash")
                        .font(.caption)
                        .foregroundColor(.red)
                        .padding(8)
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(6)
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            // 数据类型标签
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 6) {
                    ForEach(Array(record.dataTypes), id: \.self) { dataType in
                        DataTypeTag(dataType: dataType)
                    }
                }
            }
        }
        .padding(.vertical, 4)
    }
    
    private var dataTypesDescription: String {
        let types = Array(record.dataTypes)
        if types.contains(WKWebsiteDataTypeCookies) && types.contains(WKWebsiteDataTypeDiskCache) {
            return "包含Cookie、缓存等数据"
        } else if types.contains(WKWebsiteDataTypeCookies) {
            return "包含Cookie数据"
        } else if types.contains(WKWebsiteDataTypeDiskCache) {
            return "包含缓存数据"
        } else {
            return "包含\(types.count)种数据类型"
        }
    }
}

// MARK: - 数据类型标签组件
struct DataTypeTag: View {
    let dataType: String
    
    var body: some View {
        Text(dataTypeDisplayName)
            .font(.caption2)
            .padding(.horizontal, 8)
            .padding(.vertical, 3)
            .background(tagColor.opacity(0.2))
            .foregroundColor(tagColor)
            .cornerRadius(4)
    }
    
    private var dataTypeDisplayName: String {
        switch dataType {
        case WKWebsiteDataTypeCookies:
            return "Cookie"
        case WKWebsiteDataTypeDiskCache:
            return "磁盘缓存"
        case WKWebsiteDataTypeMemoryCache:
            return "内存缓存"
        case WKWebsiteDataTypeLocalStorage:
            return "本地存储"
        case WKWebsiteDataTypeSessionStorage:
            return "会话存储"
        case WKWebsiteDataTypeWebSQLDatabases:
            return "SQL数据库"
        case WKWebsiteDataTypeIndexedDBDatabases:
            return "IndexedDB"
        default:
            return "其他"
        }
    }
    
    private var tagColor: Color {
        switch dataType {
        case WKWebsiteDataTypeCookies:
            return .orange
        case WKWebsiteDataTypeDiskCache, WKWebsiteDataTypeMemoryCache:
            return .blue
        case WKWebsiteDataTypeLocalStorage, WKWebsiteDataTypeSessionStorage:
            return .green
        case WKWebsiteDataTypeWebSQLDatabases, WKWebsiteDataTypeIndexedDBDatabases:
            return .purple
        default:
            return .gray
        }
    }
}

// MARK: - 预览
struct WebsiteDataDetailView_Previews: PreviewProvider {
    static var previews: some View {
        WebsiteDataDetailView(records: [])
    }
} 