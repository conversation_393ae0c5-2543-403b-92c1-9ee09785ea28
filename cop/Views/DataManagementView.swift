//
//  DataManagementView.swift
//  cop
//
//  Created by Augment Agent on 2025/6/2.
//

import SwiftUI
import WebKit

// MARK: - 数据模型
struct CookieDetail {
    let domain: String
    let dataTypes: [String]
    let estimatedSize: String
}

// MARK: - 简化的数据管理视图
struct DataManagementView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var browserViewModel: NewWebBrowserViewModel
    @State private var isClearing = false
    @State private var showingClearConfirmation = false
    @State private var confirmationAction: () -> Void = {}
    @State private var confirmationTitle = ""
    @State private var confirmationMessage = ""
    
    // 简化的统计数据
    @State private var historyCount: Int = 0
    @State private var bookmarkCount: Int = 0
    @State private var tabCount: Int = 0
    @State private var websiteDataCount: Int = 0
    
    // 详细的网站数据
    @State private var websiteDataRecords: [WKWebsiteDataRecord] = []
    @State private var cookieDetails: [CookieDetail] = []
    @State private var cacheSize: String = "计算中..."
    @State private var localStorageSize: String = "计算中..."
    
    var body: some View {
        List {
            // 简化的数据概览
            dataOverviewSection
            
            // 详细数据展示
            detailedDataSection
            
            // 浏览器数据操作
            browserDataSection
            
            // 网站数据操作
            websiteDataSection
        }
        .navigationTitle("数据管理")
        .navigationBarTitleDisplayMode(.large)
        .onAppear {
            loadDataCounts()
        }
        .alert(confirmationTitle, isPresented: $showingClearConfirmation) {
            Button("取消", role: .cancel) { }
            Button("清除", role: .destructive) {
                confirmationAction()
            }
        } message: {
            Text(confirmationMessage)
        }
        .overlay {
            if isClearing {
                ZStack {
                    Color.black.opacity(0.3)
                        .ignoresSafeArea()
                    
                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.2)
                        Text("正在清除数据...")
                            .font(.headline)
                    }
                    .padding(24)
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(radius: 10)
                }
            }
        }
    }
    
    // MARK: - 数据概览区域
    private var dataOverviewSection: some View {
        Section {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                DataCountCard(
                    title: "浏览历史",
                    count: historyCount,
                    icon: "clock.fill",
                    color: .blue
                )
                
                DataCountCard(
                    title: "书签",
                    count: bookmarkCount,
                    icon: "book.fill",
                    color: .orange
                )
                
                DataCountCard(
                    title: "标签页",
                    count: tabCount,
                    icon: "rectangle.on.rectangle",
                    color: .green
                )
                
                DataCountCard(
                    title: "网站数据",
                    count: websiteDataCount,
                    icon: "globe",
                    color: .purple
                )
            }
            .padding(.vertical, 8)
        } header: {
            Text("数据概览")
        }
    }
    
    // MARK: - 详细数据展示区域
    private var detailedDataSection: some View {
        Section {
            // 数据统计卡片网格
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                DataDetailCard(
                    title: "网络缓存",
                    value: cacheSize,
                    icon: "externaldrive.fill",
                    color: .blue,
                    description: "磁盘和内存缓存"
                )
                
                DataDetailCard(
                    title: "Cookie",
                    value: "\(cookieDetails.count)",
                    unit: "网站",
                    icon: "doc.text.fill",
                    color: .orange,
                    description: "网站身份验证"
                )
                
                DataDetailCard(
                    title: "本地存储",
                    value: localStorageSize,
                    icon: "internaldrive.fill",
                    color: .green,
                    description: "应用程序数据"
                )
            }
            .padding(.vertical, 8)
            
            // 网站数据详情入口
            if !websiteDataRecords.isEmpty {
                NavigationLink(destination: WebsiteDataDetailView(records: websiteDataRecords)) {
                    HStack {
                        Image(systemName: "globe")
                            .font(.title3)
                            .foregroundColor(.purple)
                            .frame(width: 24, height: 24)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("网站数据详情")
                                .font(.body)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                            
                            Text("查看和管理 \(websiteDataRecords.count) 个网站的数据")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 8)
                }
                .buttonStyle(PlainButtonStyle())
            }
        } header: {
            Text("详细数据信息")
        } footer: {
            Text("数据大小为估算值，实际大小可能有所不同")
        }
    }
    
    // MARK: - 浏览器数据操作
    private var browserDataSection: some View {
        Section {
            SimpleDataRow(
                title: "清除浏览历史",
                description: "\(historyCount) 条记录",
                icon: "clock.fill",
                color: .blue,
                isDestructive: true
            ) {
                showConfirmation(
                    title: "清除浏览历史",
                    message: "这将删除所有浏览历史记录，此操作无法撤销。"
                ) {
                    clearHistory()
                }
            }
            
            SimpleDataRow(
                title: "清除书签",
                description: "\(bookmarkCount) 个书签",
                icon: "book.fill",
                color: .orange,
                isDestructive: true
            ) {
                showConfirmation(
                    title: "清除所有书签",
                    message: "这将删除所有保存的书签，此操作无法撤销。"
                ) {
                    clearBookmarks()
                }
            }
            
            SimpleDataRow(
                title: "关闭其他标签页",
                description: "保留当前标签页",
                icon: "rectangle.on.rectangle",
                color: .green,
                isDestructive: false
            ) {
                showConfirmation(
                    title: "关闭其他标签页",
                    message: "这将关闭除当前标签页外的所有标签页。"
                ) {
                    closeOtherTabs()
                }
            }
        } header: {
            Text("浏览器数据")
        }
    }
    
    // MARK: - 网站数据操作
    private var websiteDataSection: some View {
        Section {
            SimpleDataRow(
                title: "清除Cookie",
                description: "所有网站Cookie",
                icon: "doc.text.fill",
                color: .red,
                isDestructive: true
            ) {
                showConfirmation(
                    title: "清除Cookie",
                    message: "这将清除所有网站的Cookie数据，您可能需要重新登录网站。"
                ) {
                    clearCookies()
                }
            }
            
            SimpleDataRow(
                title: "清除缓存",
                description: "所有网站缓存",
                icon: "externaldrive.fill",
                color: .red,
                isDestructive: true
            ) {
                showConfirmation(
                    title: "清除缓存",
                    message: "这将清除所有网站的缓存数据，网页加载可能会变慢。"
                ) {
                    clearCache()
                }
            }
            
            SimpleDataRow(
                title: "清除所有网站数据",
                description: "包括本地存储、数据库等",
                icon: "trash.fill",
                color: .red,
                isDestructive: true
            ) {
                showConfirmation(
                    title: "清除所有网站数据",
                    message: "这将清除所有网站的存储数据，包括登录状态、偏好设置等。"
                ) {
                    clearAllWebsiteData()
                }
            }
        } header: {
            Text("网站数据")
        } footer: {
            Text("清除网站数据将删除相关的用户偏好设置和登录状态。")
        }
    }
    
    // MARK: - 数据操作方法
    private func loadDataCounts() {
        historyCount = browserViewModel.history.count
        bookmarkCount = browserViewModel.bookmarks.count
        tabCount = browserViewModel.tabs.count
        
        // 异步获取详细的网站数据
        Task {
            await loadDetailedWebsiteData()
        }
    }
    
    private func loadDetailedWebsiteData() async {
        let allDataTypes = Set([
            WKWebsiteDataTypeCookies,
            WKWebsiteDataTypeDiskCache,
            WKWebsiteDataTypeMemoryCache,
            WKWebsiteDataTypeLocalStorage,
            WKWebsiteDataTypeSessionStorage,
            WKWebsiteDataTypeWebSQLDatabases,
            WKWebsiteDataTypeIndexedDBDatabases
        ])
        
        let records = await WKWebsiteDataStore.default().dataRecords(ofTypes: allDataTypes)
        
        await MainActor.run {
            self.websiteDataCount = records.count
            self.websiteDataRecords = records
            self.calculateDataSizes(from: records)
            self.extractCookieDetails(from: records)
        }
    }
    
    private func calculateDataSizes(from records: [WKWebsiteDataRecord]) {
        var totalCacheSize: Int64 = 0
        var totalLocalStorageSize: Int64 = 0
        var cookieSites: Set<String> = []
        
        for record in records {
            // 统计缓存数据
            if record.dataTypes.contains(WKWebsiteDataTypeDiskCache) ||
               record.dataTypes.contains(WKWebsiteDataTypeMemoryCache) {
                // 估算缓存大小（WebKit不提供精确大小）
                totalCacheSize += 1024 * 1024 // 每个网站估算1MB
            }
            
            // 统计本地存储
            if record.dataTypes.contains(WKWebsiteDataTypeLocalStorage) ||
               record.dataTypes.contains(WKWebsiteDataTypeSessionStorage) {
                totalLocalStorageSize += 512 * 1024 // 每个网站估算512KB
            }
            
            // 统计Cookie网站
            if record.dataTypes.contains(WKWebsiteDataTypeCookies) {
                cookieSites.insert(record.displayName)
            }
        }
        
        cacheSize = ByteCountFormatter.string(fromByteCount: totalCacheSize, countStyle: .file)
        localStorageSize = ByteCountFormatter.string(fromByteCount: totalLocalStorageSize, countStyle: .file)
    }
    
    private func extractCookieDetails(from records: [WKWebsiteDataRecord]) {
        var details: [CookieDetail] = []
        
        for record in records {
            if record.dataTypes.contains(WKWebsiteDataTypeCookies) {
                let detail = CookieDetail(
                    domain: record.displayName,
                    dataTypes: Array(record.dataTypes),
                    estimatedSize: "~10KB" // WebKit不提供精确大小
                )
                details.append(detail)
            }
        }
        
        cookieDetails = details.sorted { $0.domain < $1.domain }
    }
    
    private func showConfirmation(title: String, message: String, action: @escaping () -> Void) {
        confirmationTitle = title
        confirmationMessage = message
        confirmationAction = action
        showingClearConfirmation = true
    }
    
    private func clearHistory() {
        isClearing = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            browserViewModel.clearHistory()
            loadDataCounts()
            isClearing = false
        }
    }
    
    private func clearBookmarks() {
        isClearing = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            browserViewModel.bookmarks.removeAll()
            browserViewModel.saveTabs()
            loadDataCounts()
            isClearing = false
        }
    }
    
    private func closeOtherTabs() {
        if browserViewModel.tabs.count > 1 {
            let currentTab = browserViewModel.currentTab
            browserViewModel.tabs = currentTab != nil ? [currentTab!] : []
            browserViewModel.currentTabIndex = 0
            browserViewModel.saveTabs()
            loadDataCounts()
        }
    }
    
    private func clearCookies() {
        isClearing = true
        Task {
            await WKWebsiteDataStore.default().removeData(
                ofTypes: [WKWebsiteDataTypeCookies],
                modifiedSince: .distantPast
            )
            
            await MainActor.run {
                loadDataCounts()
                isClearing = false
            }
        }
    }
    
    private func clearCache() {
        isClearing = true
        Task {
            await WKWebsiteDataStore.default().removeData(
                ofTypes: [WKWebsiteDataTypeDiskCache, WKWebsiteDataTypeMemoryCache],
                modifiedSince: .distantPast
            )
            
            await MainActor.run {
                loadDataCounts()
                isClearing = false
            }
        }
    }
    
    private func clearAllWebsiteData() {
        isClearing = true
        Task {
            let allTypes = Set([
                WKWebsiteDataTypeCookies,
                WKWebsiteDataTypeDiskCache,
                WKWebsiteDataTypeMemoryCache,
                WKWebsiteDataTypeLocalStorage,
                WKWebsiteDataTypeSessionStorage,
                WKWebsiteDataTypeWebSQLDatabases,
                WKWebsiteDataTypeIndexedDBDatabases
            ])
            
            await WKWebsiteDataStore.default().removeData(
                ofTypes: allTypes,
                modifiedSince: .distantPast
            )
            
            await MainActor.run {
                loadDataCounts()
                isClearing = false
            }
        }
    }
}

// MARK: - 简化的数据统计卡片
struct DataCountCard: View {
    let title: String
    let count: Int
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text("\(count)")
                .font(.title2)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - 详细数据卡片
struct DataDetailCard: View {
    let title: String
    let value: String
    var unit: String = ""
    let icon: String
    let color: Color
    let description: String
    
    var body: some View {
        VStack(spacing: 8) {
            // 图标
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(height: 24)
            
            // 数值显示
            HStack(alignment: .firstTextBaseline, spacing: 2) {
                Text(value)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                if !unit.isEmpty {
                    Text(unit)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 标题
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
            
            // 描述
            Text(description)
                .font(.caption2)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .padding(.horizontal, 8)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color(.systemGray6))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(color.opacity(0.2), lineWidth: 1)
        )
    }
}

// MARK: - 详细数据行组件
struct DetailedDataRow: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let showChevron: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if showChevron {
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 简化的数据操作行
struct SimpleDataRow: View {
    let title: String
    let description: String
    let icon: String
    let color: Color
    let isDestructive: Bool
    let action: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(isDestructive ? .red : .primary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button(action: action) {
                Text(isDestructive ? "清除" : "执行")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isDestructive ? .red : .blue)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(isDestructive ? Color.red.opacity(0.1) : Color.blue.opacity(0.1))
                    )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.vertical, 4)
    }
}

// MARK: - 预览
struct DataManagementView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            DataManagementView()
                .environmentObject(NewWebBrowserViewModel())
        }
    }
}
