//
//  AdBlockSettingsView.swift
//  cop
//
//  Created by Augment Agent on 2025/6/16.
//

import SwiftUI

/// 广告屏蔽设置视图
struct AdBlockSettingsView: View {
    @StateObject private var adBlockService = AdBlockService.shared
    @Binding var navigationPath: NavigationPath
    @Environment(\.dismiss) private var dismiss

    @State private var showingAddRuleSheet = false
    @State private var showingAddWhitelistSheet = false
    @State private var newRule = ""
    @State private var newWhitelistHost = ""
    
    var body: some View {
        List {
            // 主开关
            Section {
                Toggle("启用广告屏蔽", isOn: $adBlockService.isEnabled)
                    .tint(.blue)
            } header: {
                Text("广告屏蔽")
            } footer: {
                Text("启用后将自动屏蔽网页中的广告内容")
            }

            // EasyList 管理
            if adBlockService.isEnabled {
                Section {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("EasyList 增强屏蔽")
                                .font(.headline)
                            Text("使用 EasyList 规则库增强广告屏蔽效果")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        Toggle("", isOn: $adBlockService.easyListEnabled)
                            .onChange(of: adBlockService.easyListEnabled) {
                                adBlockService.toggleEasyList()
                            }
                    }

                    Button(action: {
                        navigationPath.append(BrowserNavigationDestination.easyListManagement)
                    }) {
                        HStack {
                            Image(systemName: "list.bullet.rectangle")
                                .foregroundColor(.blue)
                            Text("管理 EasyList 订阅")
                                .foregroundColor(.primary)
                            Spacer()
                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                } header: {
                    Text("EasyList 规则库")
                } footer: {
                    Text("EasyList 是业界标准的广告屏蔽规则库，包含数万条精确的屏蔽规则")
                }
            }
            
            // 统计信息
            if adBlockService.isEnabled {
                Section("统计信息") {
                    StatisticsRow(
                        title: "已屏蔽广告",
                        value: "\(adBlockService.statistics.totalBlocked)",
                        icon: "shield.fill",
                        color: .green
                    )
                    
                    StatisticsRow(
                        title: "允许通过",
                        value: "\(adBlockService.statistics.totalAllowed)",
                        icon: "checkmark.circle.fill",
                        color: .blue
                    )
                    
                    StatisticsRow(
                        title: "屏蔽率",
                        value: String(format: "%.1f%%", adBlockService.statistics.blockingRate * 100),
                        icon: "chart.pie.fill",
                        color: .orange
                    )
                    
                    Button("重置统计") {
                        adBlockService.resetStatistics()
                    }
                    .foregroundColor(.red)
                }
            }
            
            // 白名单管理
            Section {
                ForEach(Array(adBlockService.whitelist), id: \.self) { host in
                    HStack {
                        Image(systemName: "globe")
                            .foregroundColor(.green)
                        Text(host)
                        Spacer()
                    }
                }
                .onDelete(perform: deleteWhitelistItems)
                
                Button("添加网站到白名单") {
                    showingAddWhitelistSheet = true
                }
                .foregroundColor(.blue)
            } header: {
                Text("白名单")
            } footer: {
                Text("白名单中的网站不会被屏蔽广告")
            }
            
            // 自定义规则
            Section {
                ForEach(adBlockService.customRules, id: \.self) { rule in
                    HStack {
                        Image(systemName: "text.badge.minus")
                            .foregroundColor(.red)
                        Text(rule)
                            .font(.system(.caption, design: .monospaced))
                        Spacer()
                    }
                }
                .onDelete(perform: deleteCustomRules)
                
                Button("添加自定义规则") {
                    showingAddRuleSheet = true
                }
                .foregroundColor(.blue)
            } header: {
                Text("自定义规则")
            } footer: {
                Text("支持CSS选择器格式，如 .ad-banner 或 [data-ad]")
            }
        }
        .navigationTitle("广告屏蔽")
        .navigationBarTitleDisplayMode(.large)
        .sheet(isPresented: $showingAddRuleSheet) {
            AddRuleSheet(newRule: $newRule) {
                adBlockService.addCustomRule(newRule)
                newRule = ""
            }
        }
        .sheet(isPresented: $showingAddWhitelistSheet) {
            AddWhitelistSheet(newHost: $newWhitelistHost) {
                adBlockService.addToWhitelist(newWhitelistHost)
                newWhitelistHost = ""
            }
        }
    }
    
    private func deleteWhitelistItems(offsets: IndexSet) {
        let hosts = Array(adBlockService.whitelist)
        for index in offsets {
            adBlockService.removeFromWhitelist(hosts[index])
        }
    }
    
    private func deleteCustomRules(offsets: IndexSet) {
        for index in offsets {
            let rule = adBlockService.customRules[index]
            adBlockService.removeCustomRule(rule)
        }
    }
}

// MARK: - 统计信息行
struct StatisticsRow: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)
            
            Text(title)
            
            Spacer()
            
            Text(value)
                .font(.system(.body, design: .rounded))
                .fontWeight(.medium)
                .foregroundColor(color)
        }
    }
}

// MARK: - 添加规则弹窗
struct AddRuleSheet: View {
    @Binding var newRule: String
    let onAdd: () -> Void
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("CSS选择器规则")
                        .font(.headline)
                    
                    Text("输入CSS选择器来屏蔽特定元素，例如：")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("• .advertisement")
                        Text("• [data-ad-slot]")
                        Text("• #sidebar-ads")
                    }
                    .font(.system(.caption, design: .monospaced))
                    .foregroundColor(.secondary)
                }
                
                TextField("输入CSS选择器", text: $newRule)
                    .textFieldStyle(.roundedBorder)
                    .font(.system(.body, design: .monospaced))
                
                Spacer()
            }
            .padding()
            .navigationTitle("添加规则")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("添加") {
                        onAdd()
                        dismiss()
                    }
                    .disabled(newRule.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
    }
}

// MARK: - 添加白名单弹窗
struct AddWhitelistSheet: View {
    @Binding var newHost: String
    let onAdd: () -> Void
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("网站域名")
                        .font(.headline)
                    
                    Text("输入要添加到白名单的网站域名：")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("• example.com")
                        Text("• news.website.com")
                        Text("• blog.example.org")
                    }
                    .font(.system(.caption, design: .monospaced))
                    .foregroundColor(.secondary)
                }
                
                TextField("输入域名", text: $newHost)
                    .textFieldStyle(.roundedBorder)
                    .keyboardType(.URL)
                    .autocapitalization(.none)
                
                Spacer()
            }
            .padding()
            .navigationTitle("添加白名单")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("添加") {
                        onAdd()
                        dismiss()
                    }
                    .disabled(newHost.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
    }
}

#Preview {
    AdBlockSettingsView(navigationPath: .constant(NavigationPath()))
}
