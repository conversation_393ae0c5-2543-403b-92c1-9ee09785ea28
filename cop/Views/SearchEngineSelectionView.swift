//
//  SearchEngineSelectionView.swift
//  cop
//
//  Created by 阿亮 on 2025/6/2.
//

import SwiftUI

// MARK: - 搜索引擎选择视图
struct SearchEngineSelectionView: View {
    @ObservedObject var browserViewModel: NewWebBrowserViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 0) {
            // 当前选择指示
            currentSelectionHeader
            
            // 搜索引擎选项列表
            ScrollView {
                LazyVStack(spacing: AppDesignSystem.Spacing.md) {
                    ForEach(BrowserSearchEngine.allCases, id: \.self) { searchEngine in
                        SearchEngineCard(
                            searchEngine: searchEngine,
                            isSelected: browserViewModel.currentSearchEngine == searchEngine,
                            onSelect: {
                                browserViewModel.switchSearchEngine(to: searchEngine)
                                dismiss()
                            }
                        )
                    }
                }
                .padding(.horizontal, AppDesignSystem.Spacing.lg)
                .padding(.vertical, AppDesignSystem.Spacing.md)
            }
        }
        .navigationTitle("默认搜索引擎")
        .navigationBarTitleDisplayMode(.large)
    }
    
    // MARK: - 当前选择头部
    private var currentSelectionHeader: some View {
        VStack(spacing: AppDesignSystem.Spacing.sm) {
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.title2)

                VStack(alignment: .leading, spacing: 2) {
                    Text("当前选择")
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(.secondary)

                    Text(browserViewModel.currentSearchEngine.displayName)
                        .font(AppDesignSystem.Typography.body)
                        .fontWeight(.medium)
                }

                Spacer()
            }
            .padding(.horizontal, AppDesignSystem.Spacing.lg)
            .padding(.vertical, AppDesignSystem.Spacing.md)

            Divider()
        }
        .background(AppDesignSystem.Colors.background)
    }
}

// MARK: - 搜索引擎卡片
struct SearchEngineCard: View {
    let searchEngine: BrowserSearchEngine
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: AppDesignSystem.Spacing.md) {
                // 图标
                Image(systemName: searchEngine.icon)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : colorForSearchEngine(searchEngine))
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(isSelected ? colorForSearchEngine(searchEngine) : AppDesignSystem.Colors.backgroundSecondary)
                    )

                // 内容
                VStack(alignment: .leading, spacing: 4) {
                    Text(searchEngine.displayName)
                        .font(AppDesignSystem.Typography.headline)
                        .foregroundColor(AppDesignSystem.Colors.text)

                    Text(searchEngine.description)
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)

                    Text(searchEngine.searchTemplate)
                        .font(AppDesignSystem.Typography.caption2)
                        .foregroundColor(Color(.tertiaryLabel))
                        .lineLimit(1)
                }

                Spacer()

                // 选择指示器
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.title3)
                }
            }
            .padding(AppDesignSystem.Spacing.lg)
            .background(
                RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.lg)
                    .fill(AppDesignSystem.Colors.backgroundSecondary)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.lg)
                            .stroke(
                                isSelected ? colorForSearchEngine(searchEngine) : Color.clear,
                                lineWidth: 2
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 辅助方法
    private func colorForSearchEngine(_ searchEngine: BrowserSearchEngine) -> Color {
        switch searchEngine {
        case .google: return .blue
        case .bing: return .orange
        case .duckduckgo: return .red
        case .baidu: return .blue
        case .yahoo: return .purple
        case .yandex: return .red
        }
    }
}

// MARK: - 预览
struct SearchEngineSelectionView_Previews: PreviewProvider {
    static var previews: some View {
        SearchEngineSelectionView(browserViewModel: NewWebBrowserViewModel())
    }
}
