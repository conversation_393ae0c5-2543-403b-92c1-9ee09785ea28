//
//  EasyListManagementView.swift
//  cop
//
//  Created by Augment Agent on 2025/6/16.
//

import SwiftUI

/// EasyList订阅管理视图
struct EasyListManagementView: View {
    @StateObject private var easyListService = EasyListService.shared
    @StateObject private var adBlockService = AdBlockService.shared
    @Environment(\.dismiss) private var dismiss
    
    @State private var showingAddSubscription = false
    @State private var showingSubscriptionDetail: EasyListSubscription?
    @State private var isRefreshing = false
    
    var body: some View {
        List {
            // 总体状态部分
            Section {
                overviewSection
            } header: {
                Text("概览")
            }
            
            // 订阅列表部分
            Section {
                ForEach(easyListService.subscriptions) { subscription in
                    SubscriptionRowView(
                        subscription: subscription,
                        onToggle: {
                            easyListService.toggleSubscription(subscription)
                        },
                        onTap: {
                            showingSubscriptionDetail = subscription
                        }
                    )
                }
                .onDelete(perform: deleteSubscriptions)
            } header: {
                HStack {
                    Text("订阅列表")
                    Spacer()
                    Button("添加") {
                        showingAddSubscription = true
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            
            // 统计信息部分
            Section {
                statisticsSection
            } header: {
                Text("统计信息")
            }
        }
        .navigationTitle("EasyList 管理")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("刷新") {
                    refreshSubscriptions()
                }
                .disabled(easyListService.isUpdating)
            }
        }
        .refreshable {
            await refreshSubscriptionsAsync()
        }
        .sheet(isPresented: $showingAddSubscription) {
            AddSubscriptionView()
        }
        .sheet(item: $showingSubscriptionDetail) { subscription in
            SubscriptionDetailView(subscription: subscription)
        }
    }
    
    // MARK: - 视图组件
    
    private var overviewSection: some View {
        VStack(spacing: 12) {
            // EasyList开关
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("EasyList 广告屏蔽")
                        .font(.headline)
                    Text("使用 EasyList 规则增强广告屏蔽效果")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Toggle("", isOn: $adBlockService.easyListEnabled)
                    .onChange(of: adBlockService.easyListEnabled) {
                        adBlockService.toggleEasyList()
                    }
            }
            
            // 更新状态
            if easyListService.isUpdating {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("正在更新订阅...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                }
            }
            
            // 错误信息
            if let error = easyListService.lastError {
                HStack {
                    Image(systemName: "exclamationmark.triangle")
                        .foregroundColor(.orange)
                    Text(error)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                }
            }
        }
        .padding(.vertical, 4)
    }
    
    private var statisticsSection: some View {
        VStack(spacing: 8) {
            StatisticRowView(
                title: "总订阅数",
                value: "\(easyListService.statistics.totalSubscriptions)"
            )
            
            StatisticRowView(
                title: "已启用订阅",
                value: "\(easyListService.statistics.enabledSubscriptions)"
            )
            
            StatisticRowView(
                title: "总规则数",
                value: "\(easyListService.statistics.totalRules)"
            )
            
            StatisticRowView(
                title: "网络屏蔽规则",
                value: "\(easyListService.statistics.networkBlockingRules)"
            )
            
            StatisticRowView(
                title: "元素隐藏规则",
                value: "\(easyListService.statistics.elementHidingRules)"
            )
            
            if let lastCompilation = easyListService.statistics.lastCompilationTime {
                StatisticRowView(
                    title: "上次编译",
                    value: RelativeDateTimeFormatter().localizedString(for: lastCompilation, relativeTo: Date())
                )
            }
        }
    }
    
    // MARK: - 操作方法
    
    private func deleteSubscriptions(offsets: IndexSet) {
        for index in offsets {
            let subscription = easyListService.subscriptions[index]
            easyListService.removeSubscription(subscription)
        }
    }
    
    private func refreshSubscriptions() {
        isRefreshing = true
        Task {
            await easyListService.updateAllSubscriptions()
            await MainActor.run {
                isRefreshing = false
            }
        }
    }
    
    private func refreshSubscriptionsAsync() async {
        await easyListService.updateAllSubscriptions()
    }
}

// MARK: - 订阅行视图
struct SubscriptionRowView: View {
    let subscription: EasyListSubscription
    let onToggle: () -> Void
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(subscription.name)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(subscription.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    HStack {
                        Text("\(subscription.ruleCount) 条规则")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        Text(subscription.updateStatus.description)
                            .font(.caption2)
                            .foregroundColor(statusColor(for: subscription.updateStatus))
                    }
                }
                
                Spacer()
                
                Toggle("", isOn: .constant(subscription.isEnabled))
                    .onTapGesture {
                        onToggle()
                    }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func statusColor(for status: SubscriptionUpdateStatus) -> Color {
        switch status {
        case .upToDate:
            return .green
        case .needsUpdate:
            return .orange
        case .neverUpdated:
            return .gray
        case .error:
            return .red
        }
    }
}

// MARK: - 统计行视图
struct StatisticRowView: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
        }
    }
}

// MARK: - 添加订阅视图
struct AddSubscriptionView: View {
    @StateObject private var easyListService = EasyListService.shared
    @Environment(\.dismiss) private var dismiss

    @State private var name = ""
    @State private var urlString = ""
    @State private var description = ""
    @State private var showingDefaultSubscriptions = false

    var body: some View {
        NavigationView {
            Form {
                Section {
                    TextField("订阅名称", text: $name)
                    TextField("订阅URL", text: $urlString)
                        .keyboardType(.URL)
                        .autocapitalization(.none)
                    TextField("描述", text: $description, axis: .vertical)
                        .lineLimit(3...6)
                } header: {
                    Text("订阅信息")
                }

                Section {
                    Button("添加默认订阅") {
                        showingDefaultSubscriptions = true
                    }
                } header: {
                    Text("快速添加")
                } footer: {
                    Text("包含 EasyList、EasyList China 和 EasyPrivacy 等常用订阅")
                }
            }
            .navigationTitle("添加订阅")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("添加") {
                        addSubscription()
                    }
                    .disabled(!isValidInput)
                }
            }
            .sheet(isPresented: $showingDefaultSubscriptions) {
                DefaultSubscriptionsView()
            }
        }
    }

    private var isValidInput: Bool {
        !name.isEmpty && !urlString.isEmpty && URL(string: urlString) != nil
    }

    private func addSubscription() {
        guard let url = URL(string: urlString) else { return }

        easyListService.addSubscription(
            name: name,
            url: url,
            description: description
        )

        dismiss()
    }
}

// MARK: - 默认订阅视图
struct DefaultSubscriptionsView: View {
    @StateObject private var easyListService = EasyListService.shared
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List {
                ForEach(DefaultEasyListSubscriptions.all, id: \.url) { subscription in
                    VStack(alignment: .leading, spacing: 4) {
                        Text(subscription.name)
                            .font(.headline)

                        Text(subscription.description)
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(subscription.url.absoluteString)
                            .font(.caption2)
                            .foregroundColor(.blue)
                    }
                    .padding(.vertical, 4)
                    .onTapGesture {
                        addDefaultSubscription(subscription)
                    }
                }
            }
            .navigationTitle("默认订阅")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("全部添加") {
                        easyListService.addDefaultSubscriptions()
                        dismiss()
                    }
                }
            }
        }
    }

    private func addDefaultSubscription(_ subscription: EasyListSubscription) {
        easyListService.addSubscription(
            name: subscription.name,
            url: subscription.url,
            description: subscription.description
        )
        dismiss()
    }
}

// MARK: - 订阅详情视图
struct SubscriptionDetailView: View {
    let subscription: EasyListSubscription
    @StateObject private var easyListService = EasyListService.shared
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List {
                Section {
                    DetailRowView(title: "名称", value: subscription.name)
                    DetailRowView(title: "URL", value: subscription.url.absoluteString)
                    DetailRowView(title: "描述", value: subscription.description)
                } header: {
                    Text("基本信息")
                }

                Section {
                    DetailRowView(title: "规则数量", value: "\(subscription.ruleCount)")
                    DetailRowView(title: "更新间隔", value: formatTimeInterval(subscription.updateInterval))
                    DetailRowView(title: "状态", value: subscription.updateStatus.description)

                    if let lastUpdated = subscription.lastUpdated {
                        DetailRowView(
                            title: "上次更新",
                            value: DateFormatter.localizedString(from: lastUpdated, dateStyle: .medium, timeStyle: .short)
                        )
                    }

                    if let errorMessage = subscription.errorMessage, !errorMessage.isEmpty {
                        DetailRowView(title: "错误信息", value: errorMessage)
                    }
                } header: {
                    Text("状态信息")
                }

                Section {
                    Button("立即更新") {
                        updateSubscription()
                    }
                    .disabled(easyListService.isUpdating)

                    Button("删除订阅") {
                        deleteSubscription()
                    }
                    .foregroundColor(.red)
                } header: {
                    Text("操作")
                }
            }
            .navigationTitle("订阅详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }

    private func updateSubscription() {
        Task {
            await easyListService.updateSubscription(subscription)
        }
    }

    private func deleteSubscription() {
        easyListService.removeSubscription(subscription)
        dismiss()
    }

    private func formatTimeInterval(_ interval: TimeInterval) -> String {
        let hours = Int(interval) / 3600
        if hours < 24 {
            return "\(hours) 小时"
        } else {
            let days = hours / 24
            return "\(days) 天"
        }
    }
}

// MARK: - 详情行视图
struct DetailRowView: View {
    let title: String
    let value: String

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)

            Text(value)
                .font(.body)
                .textSelection(.enabled)
        }
        .padding(.vertical, 2)
    }
}

// MARK: - 预览
struct EasyListManagementView_Previews: PreviewProvider {
    static var previews: some View {
        EasyListManagementView()
    }
}
