//
//  TabSwitcherView.swift
//  cop
//
//  Created by Augment Agent on 2025/6/2.
//

import SwiftUI

// MARK: - 新标签页切换器视图
struct NewTabSwitcherView: View {
    @ObservedObject var browserViewModel: NewWebBrowserViewModel
    @Environment(\.dismiss) private var dismiss

    let onNewTabCreated: (() -> Void)?

    init(browserViewModel: NewWebBrowserViewModel, onNewTabCreated: (() -> Void)? = nil) {
        self.browserViewModel = browserViewModel
        self.onNewTabCreated = onNewTabCreated
    }
    
    var body: some View {
        BrowserSheetContainer(title: "标签页管理", onDismiss: { dismiss() }) {
            VStack(spacing: 0) {
                // 顶部统计信息
                tabStatisticsHeader
                
                // 标签页网格
                if browserViewModel.tabs.isEmpty {
                    emptyStateView
                } else {
                    tabGridView
                }
            }
        }
    }
    
    // MARK: - 标签页统计头部
    private var tabStatisticsHeader: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("已打开 \(browserViewModel.tabs.count) 个标签页")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text("轻触选择，滑动删除")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button("新建标签页") {
                browserViewModel.createNewTab()
                onNewTabCreated?()
                dismiss()
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.small)
        }
        .padding(.horizontal, AppDesignSystem.Spacing.lg)
        .padding(.vertical, AppDesignSystem.Spacing.md)
        .background(Color(UIColor.systemGray6))
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: AppDesignSystem.Spacing.xl) {
            Image(systemName: "square.on.square.dashed")
                .font(.system(size: 64))
                .foregroundColor(AppDesignSystem.Colors.iconSecondary)

            VStack(spacing: AppDesignSystem.Spacing.sm) {
                Text("没有打开的标签页")
                    .font(AppDesignSystem.Typography.title3)
                    .foregroundColor(AppDesignSystem.Colors.text)

                Text("创建一个新标签页开始浏览")
                    .font(AppDesignSystem.Typography.callout)
                    .foregroundColor(AppDesignSystem.Colors.textSecondary)
            }

            Button("创建新标签页") {
                browserViewModel.createNewTab()
                onNewTabCreated?()
                dismiss()
            }
            .buttonStyle(.borderedProminent)
            .frame(maxWidth: 200)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - 标签页网格视图
    private var tabGridView: some View {
        ScrollView {
            LazyVStack(spacing: AppDesignSystem.Spacing.md) {
                ForEach(Array(browserViewModel.tabs.enumerated()), id: \.element.id) { index, tab in
                    OptimizedTabCard(
                        tab: tab,
                        isSelected: index == browserViewModel.currentTabIndex,
                        onSelect: {
                            browserViewModel.selectTab(at: index)
                            dismiss()
                        },
                        onClose: {
                            browserViewModel.closeTab(at: index)
                        }
                    )
                }
            }
            .padding(AppDesignSystem.Spacing.lg)
        }
    }
}

// MARK: - 优化后的标签页卡片（横向布局）
struct OptimizedTabCard: View {
    @ObservedObject var tab: NewBrowserTab
    let isSelected: Bool
    let onSelect: () -> Void
    let onClose: () -> Void
    
    var body: some View {
        HStack(spacing: AppDesignSystem.Spacing.md) {
            // 左侧预览图片
            snapshotPreview
            
            // 中间信息区域
            tabInfoSection
            
            // 右侧控制按钮
            controlButtons
        }
        .padding(AppDesignSystem.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.lg)
                .fill(isSelected ? AppDesignSystem.Colors.primary.opacity(0.1) : AppDesignSystem.Colors.backgroundSecondary)
        )
        .overlay(
            RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.lg)
                .stroke(
                    isSelected ? AppDesignSystem.Colors.primary : AppDesignSystem.Colors.separator.opacity(0.3),
                    lineWidth: isSelected ? 2 : 1
                )
        )
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .shadow(
            color: isSelected ? AppDesignSystem.Colors.primary.opacity(0.2) : AppDesignSystem.Colors.shadow.opacity(0.1),
            radius: isSelected ? 8 : 4,
            x: 0,
            y: isSelected ? 4 : 2
        )
        .animation(AppDesignSystem.Animation.standard, value: isSelected)
        .contentShape(Rectangle())
        .onTapGesture {
            onSelect()
        }
    }
    
    // MARK: - 快照预览
    private var snapshotPreview: some View {
        ZStack {
            RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.md)
                .fill(AppDesignSystem.Colors.backgroundTertiary)
                .frame(width: 80, height: 60)
            
            Group {
                // 简化版本：暂时移除快照功能，使用默认图标
                VStack(spacing: 4) {
                    if let url = tab.url {
                        AsyncImage(url: faviconURL(for: url)) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                        } placeholder: {
                            Image(systemName: "globe")
                                .foregroundColor(AppDesignSystem.Colors.iconSecondary)
                        }
                        .frame(width: 24, height: 24)
                    } else {
                        Image(systemName: "globe")
                            .font(.title2)
                            .foregroundColor(AppDesignSystem.Colors.iconSecondary)
                    }
                }
            }
            
            // 加载指示器
            if tab.isLoading {
                RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.md)
                    .fill(Color.black.opacity(0.5))
                    .frame(width: 80, height: 60)
                
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(0.7)
            }
        }
    }
    
    // MARK: - 标签页信息区域
    private var tabInfoSection: some View {
        VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
            // 标题
            Text(tab.title)
                .font(AppDesignSystem.Typography.body)
                .fontWeight(isSelected ? .semibold : .regular)
                .foregroundColor(AppDesignSystem.Colors.text)
                .lineLimit(1)
            
            // URL或状态
            HStack(spacing: 4) {
                if tab.isLoading {
                    Image(systemName: "arrow.clockwise")
                        .font(.caption)
                        .foregroundColor(AppDesignSystem.Colors.primary)
                    Text("正在加载...")
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(AppDesignSystem.Colors.primary)
                } else if let url = tab.url {
                    Image(systemName: url.scheme == "https" ? "lock.fill" : "globe")
                        .font(.caption)
                        .foregroundColor(url.scheme == "https" ? .green : .gray)
                    
                    Text(url.host ?? url.absoluteString)
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                        .lineLimit(1)
                } else {
                    Text("新标签页")
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                }
            }
            
            // 最后活跃时间
            if !isSelected {
                Text(timeAgoString(from: tab.lastActiveDate))
                    .font(AppDesignSystem.Typography.caption2)
                    .foregroundColor(AppDesignSystem.Colors.textSecondary)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - 控制按钮
    private var controlButtons: some View {
        VStack(spacing: AppDesignSystem.Spacing.sm) {
            // 关闭按钮
            Button(action: onClose) {
                Image(systemName: "xmark.circle.fill")
                    .font(.title3)
                    .foregroundColor(.red)
                    .background(
                        Circle()
                            .fill(Color.white)
                            .frame(width: 20, height: 20)
                    )
            }
            .buttonStyle(ScaleButtonStyle())
            
            // 选中指示器
            if isSelected {
                Circle()
                    .fill(AppDesignSystem.Colors.primary)
                    .frame(width: 8, height: 8)
            }
        }
    }
    
    // MARK: - 辅助方法
    private func faviconURL(for url: URL) -> URL? {
        guard let host = url.host else { return nil }
        return URL(string: "https://www.google.com/s2/favicons?domain=\(host)&sz=32")
    }
    
    private func timeAgoString(from date: Date) -> String {
        let interval = Date().timeIntervalSince(date)
        
        if interval < 60 {
            return "刚刚"
        } else if interval < 3600 {
            let minutes = Int(interval / 60)
            return "\(minutes)分钟前"
        } else if interval < 86400 {
            let hours = Int(interval / 3600)
            return "\(hours)小时前"
        } else {
            let days = Int(interval / 86400)
            return "\(days)天前"
        }
    }
}

// MARK: - 预览
struct NewTabSwitcherView_Previews: PreviewProvider {
    static var previews: some View {
        NewTabSwitcherView(browserViewModel: NewWebBrowserViewModel())
    }
}
