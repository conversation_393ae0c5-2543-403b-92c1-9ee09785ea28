//
//  UserAgentSelectionView.swift
//  cop
//
//  Created by 阿亮 on 2025/6/2.
//

import SwiftUI

// MARK: - 用户代理选择视图
struct UserAgentSelectionView: View {
    @ObservedObject var browserViewModel: NewWebBrowserViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 0) {
            // 当前选择指示
            currentSelectionHeader
            
            // 用户代理选项列表
            ScrollView {
                LazyVStack(spacing: AppDesignSystem.Spacing.md) {
                    ForEach(UserAgentType.allCases, id: \.self) { userAgent in
                        UserAgentCard(
                            userAgent: userAgent,
                            isSelected: browserViewModel.currentUserAgent == userAgent,
                            onSelect: {
                                browserViewModel.switchUserAgent(to: userAgent)
                                dismiss()
                            }
                        )
                    }
                }
                .padding(.horizontal, AppDesignSystem.Spacing.lg)
                .padding(.vertical, AppDesignSystem.Spacing.md)
            }
        }
        .navigationTitle("用户代理")
        .navigationBarTitleDisplayMode(.large)
    }
    
    // MARK: - 当前选择头部
    private var currentSelectionHeader: some View {
        VStack(spacing: AppDesignSystem.Spacing.sm) {
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.title2)

                VStack(alignment: .leading, spacing: 2) {
                    Text("当前选择")
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(.secondary)

                    Text(browserViewModel.currentUserAgent.displayName)
                        .font(AppDesignSystem.Typography.body)
                        .fontWeight(.medium)
                }

                Spacer()
            }
            .padding(.horizontal, AppDesignSystem.Spacing.lg)
            .padding(.vertical, AppDesignSystem.Spacing.md)

            Divider()
        }
        .background(AppDesignSystem.Colors.background)
    }
}

// MARK: - 用户代理卡片
struct UserAgentCard: View {
    let userAgent: UserAgentType
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: AppDesignSystem.Spacing.md) {
                // 图标
                Image(systemName: iconForUserAgent(userAgent))
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : AppDesignSystem.Colors.primary)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(isSelected ? AppDesignSystem.Colors.primary : AppDesignSystem.Colors.backgroundSecondary)
                    )

                // 内容
                VStack(alignment: .leading, spacing: 4) {
                    Text(userAgent.displayName)
                        .font(AppDesignSystem.Typography.headline)
                        .foregroundColor(AppDesignSystem.Colors.text)

                    Text(descriptionForUserAgent(userAgent))
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }

                Spacer()

                // 选择指示器
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.title3)
                }
            }
            .padding(AppDesignSystem.Spacing.lg)
            .background(
                RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.lg)
                    .fill(AppDesignSystem.Colors.backgroundSecondary)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.lg)
                            .stroke(
                                isSelected ? AppDesignSystem.Colors.primary : Color.clear,
                                lineWidth: 2
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 辅助方法
    private func iconForUserAgent(_ userAgent: UserAgentType) -> String {
        switch userAgent {
        case .windowsPC: return "desktopcomputer"
        case .macPC: return "macbook"
        case .android: return "iphone"
        case .ios: return "ipad"
        }
    }
    
    private func descriptionForUserAgent(_ userAgent: UserAgentType) -> String {
        switch userAgent {
        case .windowsPC: return "模拟Windows PC浏览器，获得完整桌面网站体验"
        case .macPC: return "模拟Mac PC浏览器，兼容性最佳的桌面体验"
        case .android: return "模拟Android设备浏览器，适合移动版网站"
        case .ios: return "模拟iOS设备浏览器，苹果生态最佳体验"
        }
    }
}

// MARK: - 预览
struct UserAgentSelectionView_Previews: PreviewProvider {
    static var previews: some View {
        UserAgentSelectionView(browserViewModel: NewWebBrowserViewModel())
    }
}
