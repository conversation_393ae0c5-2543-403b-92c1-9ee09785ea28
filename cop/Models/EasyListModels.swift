//
//  EasyListModels.swift
//  cop
//
//  Created by Augment Agent on 2025/6/16.
//

import Foundation

// MARK: - EasyList Subscription Models

/// EasyList订阅信息
struct EasyListSubscription: Codable, Identifiable, Hashable {
    let id: UUID
    let name: String
    let url: URL
    let description: String
    var isEnabled: Bool
    var lastUpdated: Date?
    var lastUpdateAttempt: Date?
    var updateInterval: TimeInterval // 更新间隔（秒）
    var ruleCount: Int
    var errorMessage: String?
    
    init(name: String, url: URL, description: String, updateInterval: TimeInterval = 86400) { // 默认24小时
        self.id = UUID()
        self.name = name
        self.url = url
        self.description = description
        self.isEnabled = true
        self.lastUpdated = nil
        self.lastUpdateAttempt = nil
        self.updateInterval = updateInterval
        self.ruleCount = 0
        self.errorMessage = nil
    }
    
    /// 是否需要更新
    var needsUpdate: Bool {
        guard isEnabled else { return false }
        guard let lastUpdated = lastUpdated else { return true }
        return Date().timeIntervalSince(lastUpdated) > updateInterval
    }
    
    /// 更新状态
    var updateStatus: SubscriptionUpdateStatus {
        if let errorMessage = errorMessage, !errorMessage.isEmpty {
            return .error(errorMessage)
        }
        if lastUpdated == nil {
            return .neverUpdated
        }
        if needsUpdate {
            return .needsUpdate
        }
        return .upToDate
    }
}

/// 订阅更新状态
enum SubscriptionUpdateStatus {
    case upToDate
    case needsUpdate
    case neverUpdated
    case error(String)
    
    var description: String {
        switch self {
        case .upToDate:
            return "最新"
        case .needsUpdate:
            return "需要更新"
        case .neverUpdated:
            return "从未更新"
        case .error(let message):
            return "错误: \(message)"
        }
    }
}

// MARK: - Filter Rule Models

/// 过滤规则类型
enum FilterRuleType: String, Codable, CaseIterable {
    case networkBlocking = "network_blocking"    // 网络屏蔽规则 ||domain.com^
    case elementHiding = "element_hiding"        // 元素隐藏规则 ##.selector
    case exception = "exception"                 // 例外规则 @@rule
    case comment = "comment"                     // 注释 !comment
    
    var displayName: String {
        switch self {
        case .networkBlocking:
            return "网络屏蔽"
        case .elementHiding:
            return "元素隐藏"
        case .exception:
            return "例外规则"
        case .comment:
            return "注释"
        }
    }
}

/// 过滤规则选项
struct FilterRuleOptions: Codable, Hashable {
    var domains: Set<String> = []           // 域名限制
    var excludedDomains: Set<String> = []   // 排除域名
    var types: Set<String> = []             // 类型限制 (script, image, etc.)
    var excludedTypes: Set<String> = []     // 排除类型
    var thirdParty: Bool? = nil             // 第三方限制
    var matchCase: Bool = false             // 大小写敏感
    var important: Bool = false             // 重要规则
    
    /// 是否为通用规则（无域名限制）
    var isGeneric: Bool {
        return domains.isEmpty && excludedDomains.isEmpty
    }
}

/// 编译后的过滤规则
struct CompiledFilterRule: Codable, Hashable {
    let id: UUID
    let originalRule: String
    let type: FilterRuleType
    let pattern: String                     // 编译后的模式
    let options: FilterRuleOptions
    let subscriptionId: UUID?               // 所属订阅ID
    let isCustom: Bool                      // 是否为自定义规则
    
    init(originalRule: String, type: FilterRuleType, pattern: String, options: FilterRuleOptions = FilterRuleOptions(), subscriptionId: UUID? = nil, isCustom: Bool = false) {
        self.id = UUID()
        self.originalRule = originalRule
        self.type = type
        self.pattern = pattern
        self.options = options
        self.subscriptionId = subscriptionId
        self.isCustom = isCustom
    }
}

// MARK: - Compiled Rule Sets

/// 编译后的规则集合
struct CompiledRuleSet: Codable {
    var networkBlockingRules: [CompiledFilterRule] = []
    var elementHidingRules: [CompiledFilterRule] = []
    var exceptionRules: [CompiledFilterRule] = []
    var domainSpecificRules: [String: [CompiledFilterRule]] = [:]  // 域名特定规则
    var genericRules: [CompiledFilterRule] = []                    // 通用规则
    
    var totalRuleCount: Int {
        return networkBlockingRules.count + elementHidingRules.count + exceptionRules.count
    }
    
    /// 添加规则
    mutating func addRule(_ rule: CompiledFilterRule) {
        switch rule.type {
        case .networkBlocking:
            networkBlockingRules.append(rule)
        case .elementHiding:
            elementHidingRules.append(rule)
        case .exception:
            exceptionRules.append(rule)
        case .comment:
            break // 忽略注释
        }
        
        // 按域名分类
        if rule.options.isGeneric {
            genericRules.append(rule)
        } else {
            for domain in rule.options.domains {
                domainSpecificRules[domain, default: []].append(rule)
            }
        }
    }
    
    /// 获取特定域名的规则
    func getRules(for domain: String) -> [CompiledFilterRule] {
        var rules = genericRules
        
        // 添加域名特定规则
        if let domainRules = domainSpecificRules[domain] {
            rules.append(contentsOf: domainRules)
        }
        
        // 检查子域名匹配
        let components = domain.components(separatedBy: ".")
        for i in 1..<components.count {
            let parentDomain = components[i...].joined(separator: ".")
            if let parentRules = domainSpecificRules[parentDomain] {
                rules.append(contentsOf: parentRules)
            }
        }
        
        return rules
    }
}

// MARK: - Default Subscriptions

/// 默认EasyList订阅
struct DefaultEasyListSubscriptions {
    static let easyList = EasyListSubscription(
        name: "EasyList",
        url: URL(string: "https://easylist.to/easylist/easylist.txt")!,
        description: "EasyList is the primary filter list that removes most adverts from international webpages, including unwanted frames, images and objects."
    )
    
    static let easyListChina = EasyListSubscription(
        name: "EasyList China",
        url: URL(string: "https://easylist-downloads.adblockplus.org/easylistchina.txt")!,
        description: "EasyList China supplement for Chinese websites."
    )
    
    static let easyPrivacy = EasyListSubscription(
        name: "EasyPrivacy",
        url: URL(string: "https://easylist.to/easylist/easyprivacy.txt")!,
        description: "EasyPrivacy is an optional supplementary filter list that completely removes all forms of tracking from the internet."
    )
    
    static let all: [EasyListSubscription] = [
        easyList,
        easyListChina,
        easyPrivacy
    ]
}

// MARK: - Statistics

/// EasyList统计信息
struct EasyListStatistics: Codable {
    var totalSubscriptions: Int = 0
    var enabledSubscriptions: Int = 0
    var totalRules: Int = 0
    var networkBlockingRules: Int = 0
    var elementHidingRules: Int = 0
    var exceptionRules: Int = 0
    var lastCompilationTime: Date?
    var compilationDuration: TimeInterval = 0
    
    var averageRulesPerSubscription: Double {
        return enabledSubscriptions > 0 ? Double(totalRules) / Double(enabledSubscriptions) : 0
    }
}
