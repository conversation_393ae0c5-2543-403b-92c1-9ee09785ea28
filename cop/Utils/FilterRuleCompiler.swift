//
//  FilterRuleCompiler.swift
//  cop
//
//  Created by Augment Agent on 2025/6/16.
//

import Foundation
import OSLog

/// 过滤规则编译器 - 优化规则匹配性能
final class FilterRuleCompiler {
    private let logger = Logger(subsystem: "com.cop.browser", category: "FilterRuleCompiler")
    
    // MARK: - 编译后的规则结构
    
    /// 编译后的网络规则
    struct CompiledNetworkRule {
        let pattern: String
        let isRegex: Bool
        let domains: Set<String>
        let excludedDomains: Set<String>
        let types: Set<String>
        let excludedTypes: Set<String>
        let thirdParty: Bool?
        let important: Bool
        let isException: Bool
        
        /// 检查URL是否匹配
        func matches(url: String, domain: String, resourceType: String, isThirdParty: Bool) -> Bool {
            // 检查域名限制
            if !domains.isEmpty && !domains.contains(domain) {
                return false
            }
            
            if excludedDomains.contains(domain) {
                return false
            }
            
            // 检查类型限制
            if !types.isEmpty && !types.contains(resourceType) {
                return false
            }
            
            if excludedTypes.contains(resourceType) {
                return false
            }
            
            // 检查第三方限制
            if let thirdPartyRequired = thirdParty, thirdPartyRequired != isThirdParty {
                return false
            }
            
            // 检查URL模式
            return matchesPattern(url: url)
        }
        
        private func matchesPattern(url: String) -> Bool {
            if isRegex {
                // 正则表达式匹配
                do {
                    let regex = try NSRegularExpression(pattern: pattern)
                    let range = NSRange(location: 0, length: url.utf16.count)
                    return regex.firstMatch(in: url, options: [], range: range) != nil
                } catch {
                    return false
                }
            } else {
                // 简单模式匹配
                return matchesSimplePattern(url: url, pattern: pattern)
            }
        }
        
        private func matchesSimplePattern(url: String, pattern: String) -> Bool {
            // 处理EasyList模式
            var processedPattern = pattern
            
            // 处理锚点
            let startsWithDomainAnchor = processedPattern.hasPrefix("||")
            let startsWithAnchor = processedPattern.hasPrefix("|") && !startsWithDomainAnchor
            let endsWithAnchor = processedPattern.hasSuffix("|")
            
            if startsWithDomainAnchor {
                processedPattern = String(processedPattern.dropFirst(2))
                // 域名锚点匹配
                return url.contains("://\(processedPattern)") || url.contains("://www.\(processedPattern)")
            }
            
            if startsWithAnchor {
                processedPattern = String(processedPattern.dropFirst())
                if endsWithAnchor {
                    processedPattern = String(processedPattern.dropLast())
                    return url == processedPattern
                } else {
                    return url.hasPrefix(processedPattern)
                }
            }
            
            if endsWithAnchor {
                processedPattern = String(processedPattern.dropLast())
                return url.hasSuffix(processedPattern)
            }
            
            // 处理分隔符
            processedPattern = processedPattern.replacingOccurrences(of: "^", with: "[^a-zA-Z0-9._%-]")
            
            // 处理通配符
            if processedPattern.contains("*") {
                let regexPattern = processedPattern.replacingOccurrences(of: "*", with: ".*")
                do {
                    let regex = try NSRegularExpression(pattern: regexPattern)
                    let range = NSRange(location: 0, length: url.utf16.count)
                    return regex.firstMatch(in: url, options: [], range: range) != nil
                } catch {
                    return false
                }
            }
            
            return url.contains(processedPattern)
        }
    }
    
    /// 编译后的元素隐藏规则
    struct CompiledElementRule {
        let selector: String
        let domains: Set<String>
        let excludedDomains: Set<String>
        let isException: Bool
        
        /// 检查域名是否匹配
        func matches(domain: String) -> Bool {
            if !domains.isEmpty && !domains.contains(domain) {
                return false
            }
            
            if excludedDomains.contains(domain) {
                return false
            }
            
            return true
        }
    }
    
    // MARK: - 编译方法
    
    /// 编译网络规则
    func compileNetworkRules(_ rules: [CompiledFilterRule]) -> [CompiledNetworkRule] {
        var compiledRules: [CompiledNetworkRule] = []
        
        for rule in rules {
            guard rule.type == .networkBlocking || rule.type == .exception else { continue }
            
            let isRegex = rule.pattern.hasPrefix("/") && rule.pattern.hasSuffix("/")
            let pattern = isRegex ? String(rule.pattern.dropFirst().dropLast()) : rule.pattern
            
            let compiledRule = CompiledNetworkRule(
                pattern: pattern,
                isRegex: isRegex,
                domains: rule.options.domains,
                excludedDomains: rule.options.excludedDomains,
                types: rule.options.types,
                excludedTypes: rule.options.excludedTypes,
                thirdParty: rule.options.thirdParty,
                important: rule.options.important,
                isException: rule.type == .exception
            )
            
            compiledRules.append(compiledRule)
        }
        
        // 按重要性排序，重要规则优先
        compiledRules.sort { $0.important && !$1.important }
        
        logger.info("编译网络规则: \(compiledRules.count) 条")
        return compiledRules
    }
    
    /// 编译元素隐藏规则
    func compileElementRules(_ rules: [CompiledFilterRule]) -> [CompiledElementRule] {
        var compiledRules: [CompiledElementRule] = []
        
        for rule in rules {
            guard rule.type == .elementHiding || (rule.type == .exception && rule.originalRule.contains("#@#")) else { continue }
            
            let compiledRule = CompiledElementRule(
                selector: rule.pattern,
                domains: rule.options.domains,
                excludedDomains: rule.options.excludedDomains,
                isException: rule.type == .exception || rule.originalRule.contains("#@#")
            )
            
            compiledRules.append(compiledRule)
        }
        
        logger.info("编译元素规则: \(compiledRules.count) 条")
        return compiledRules
    }
    
    /// 生成优化的JavaScript代码
    func generateOptimizedJavaScript(
        networkRules: [CompiledNetworkRule],
        elementRules: [CompiledElementRule],
        domain: String
    ) -> String {
        
        // 过滤适用于当前域名的规则
        let applicableNetworkRules = networkRules.filter { rule in
            rule.domains.isEmpty || rule.domains.contains(domain) || 
            rule.domains.contains(where: { domain.hasSuffix($0) })
        }
        
        let applicableElementRules = elementRules.filter { rule in
            rule.matches(domain: domain)
        }
        
        return generateJavaScriptCode(
            networkRules: applicableNetworkRules,
            elementRules: applicableElementRules
        )
    }
    
    private func generateJavaScriptCode(
        networkRules: [CompiledNetworkRule],
        elementRules: [CompiledElementRule]
    ) -> String {
        
        // 生成网络规则的JavaScript表示
        let networkRulesJS = networkRules.map { rule in
            """
            {
                pattern: '\(escapeJavaScript(rule.pattern))',
                isRegex: \(rule.isRegex),
                types: [\(rule.types.map { "'\($0)'" }.joined(separator: ","))],
                excludedTypes: [\(rule.excludedTypes.map { "'\($0)'" }.joined(separator: ","))],
                thirdParty: \(rule.thirdParty?.description ?? "null"),
                important: \(rule.important),
                isException: \(rule.isException)
            }
            """
        }.joined(separator: ",\n")
        
        // 生成元素隐藏规则的JavaScript表示
        let elementRulesJS = elementRules.filter { !$0.isException }.map { rule in
            "'\(escapeJavaScript(rule.selector))'"
        }.joined(separator: ",")
        
        let exceptionRulesJS = elementRules.filter { $0.isException }.map { rule in
            "'\(escapeJavaScript(rule.selector))'"
        }.joined(separator: ",")
        
        return """
        (function() {
            'use strict';
            
            // EasyList编译规则
            const easyListConfig = {
                networkRules: [\(networkRulesJS)],
                elementSelectors: [\(elementRulesJS)],
                exceptionSelectors: [\(exceptionRulesJS)],
                blockedCount: 0
            };
            
            // 检查URL是否应该被屏蔽
            function shouldBlockUrl(url) {
                if (!url || typeof url !== 'string') return false;
                
                const currentDomain = window.location.hostname;
                
                for (const rule of easyListConfig.networkRules) {
                    if (matchesNetworkRule(url, rule, currentDomain)) {
                        return !rule.isException;
                    }
                }
                
                return false;
            }
            
            // 匹配网络规则
            function matchesNetworkRule(url, rule, domain) {
                if (rule.isRegex) {
                    try {
                        const regex = new RegExp(rule.pattern);
                        return regex.test(url);
                    } catch (e) {
                        return false;
                    }
                } else {
                    return matchesSimplePattern(url, rule.pattern);
                }
            }
            
            // 简单模式匹配
            function matchesSimplePattern(url, pattern) {
                if (pattern.startsWith('||')) {
                    const domain = pattern.substring(2);
                    return url.includes('://' + domain) || url.includes('://www.' + domain);
                }
                
                if (pattern.includes('*')) {
                    const regexPattern = pattern.replace(/\\*/g, '.*');
                    try {
                        const regex = new RegExp(regexPattern);
                        return regex.test(url);
                    } catch (e) {
                        return false;
                    }
                }
                
                return url.includes(pattern);
            }
            
            // 隐藏元素
            function hideElements() {
                try {
                    // 应用隐藏规则
                    easyListConfig.elementSelectors.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(element => {
                            if (element && element.style.display !== 'none') {
                                element.style.display = 'none';
                                element.style.visibility = 'hidden';
                                element.style.opacity = '0';
                                element.style.height = '0';
                                element.style.width = '0';
                                easyListConfig.blockedCount++;
                            }
                        });
                    });
                    
                    // 移除例外规则匹配的元素
                    easyListConfig.exceptionSelectors.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(element => {
                            if (element) {
                                element.style.display = '';
                                element.style.visibility = '';
                                element.style.opacity = '';
                                element.style.height = '';
                                element.style.width = '';
                            }
                        });
                    });
                } catch (error) {
                    console.log('EasyList: 元素隐藏出错', error);
                }
            }
            
            // 拦截网络请求
            function interceptRequests() {
                // 拦截fetch请求
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    const url = args[0];
                    if (typeof url === 'string' && shouldBlockUrl(url)) {
                        easyListConfig.blockedCount++;
                        return Promise.reject(new Error('Blocked by EasyList'));
                    }
                    return originalFetch.apply(this, args);
                };
                
                // 拦截XMLHttpRequest
                const originalOpen = XMLHttpRequest.prototype.open;
                XMLHttpRequest.prototype.open = function(method, url, ...args) {
                    if (shouldBlockUrl(url)) {
                        easyListConfig.blockedCount++;
                        throw new Error('Blocked by EasyList');
                    }
                    return originalOpen.apply(this, [method, url, ...args]);
                };
            }
            
            // 观察DOM变化
            function observeDOM() {
                const observer = new MutationObserver(function(mutations) {
                    let shouldCheck = false;
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                            shouldCheck = true;
                        }
                    });
                    
                    if (shouldCheck) {
                        setTimeout(hideElements, 100);
                    }
                });
                
                observer.observe(document.body || document.documentElement, {
                    childList: true,
                    subtree: true
                });
            }
            
            // 初始化EasyList
            function initEasyList() {
                hideElements();
                interceptRequests();
                
                if (document.body) {
                    observeDOM();
                } else {
                    document.addEventListener('DOMContentLoaded', observeDOM);
                }
                
                // 定期检查新的广告元素
                setInterval(hideElements, 2000);
                
                console.log('🛡️ EasyList 已激活 (' + easyListConfig.elementSelectors.length + ' 条规则)');
            }
            
            // 启动
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initEasyList);
            } else {
                initEasyList();
            }
            
            // 向原生应用报告统计信息
            function reportStats() {
                if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.easyListStats) {
                    window.webkit.messageHandlers.easyListStats.postMessage({
                        blocked: easyListConfig.blockedCount,
                        timestamp: Date.now()
                    });
                }
            }
            
            // 定期报告统计信息
            setInterval(reportStats, 3000);
            
        })();
        """
    }
    
    private func escapeJavaScript(_ string: String) -> String {
        return string
            .replacingOccurrences(of: "\\", with: "\\\\")
            .replacingOccurrences(of: "'", with: "\\'")
            .replacingOccurrences(of: "\"", with: "\\\"")
            .replacingOccurrences(of: "\n", with: "\\n")
            .replacingOccurrences(of: "\r", with: "\\r")
            .replacingOccurrences(of: "\t", with: "\\t")
    }
}
