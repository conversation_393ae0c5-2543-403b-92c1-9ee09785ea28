//
//  EasyListParser.swift
//  cop
//
//  Created by Augment Agent on 2025/6/16.
//

import Foundation
import OSLog

/// EasyList格式解析器
final class EasyListParser {
    private let logger = Logger(subsystem: "com.cop.browser", category: "EasyListParser")
    
    // MARK: - 解析入口
    
    /// 解析EasyList文本内容
    func parseFilterList(_ content: String, subscriptionId: UUID) -> [CompiledFilterRule] {
        let lines = content.components(separatedBy: .newlines)
        var rules: [CompiledFilterRule] = []
        
        for (lineNumber, line) in lines.enumerated() {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            
            // 跳过空行
            guard !trimmedLine.isEmpty else { continue }
            
            do {
                if let rule = try parseRule(trimmedLine, subscriptionId: subscriptionId) {
                    rules.append(rule)
                }
            } catch {
                logger.warning("解析规则失败 (行 \(lineNumber + 1)): \(trimmedLine) - \(error.localizedDescription)")
            }
        }
        
        logger.info("解析完成: \(rules.count) 条规则")
        return rules
    }
    
    // MARK: - 规则解析
    
    /// 解析单条规则
    private func parseRule(_ line: String, subscriptionId: UUID) throws -> CompiledFilterRule? {
        // 注释规则
        if line.hasPrefix("!") {
            return CompiledFilterRule(
                originalRule: line,
                type: .comment,
                pattern: "",
                subscriptionId: subscriptionId
            )
        }
        
        // 例外规则
        if line.hasPrefix("@@") {
            return try parseExceptionRule(line, subscriptionId: subscriptionId)
        }
        
        // 元素隐藏规则
        if line.contains("##") || line.contains("#@#") || line.contains("#?#") {
            return try parseElementHidingRule(line, subscriptionId: subscriptionId)
        }
        
        // 网络屏蔽规则
        return try parseNetworkBlockingRule(line, subscriptionId: subscriptionId)
    }
    
    /// 解析例外规则
    private func parseExceptionRule(_ line: String, subscriptionId: UUID) throws -> CompiledFilterRule {
        let ruleContent = String(line.dropFirst(2)) // 移除 "@@"
        let (pattern, options) = try parseRuleWithOptions(ruleContent)
        
        return CompiledFilterRule(
            originalRule: line,
            type: .exception,
            pattern: pattern,
            options: options,
            subscriptionId: subscriptionId
        )
    }
    
    /// 解析元素隐藏规则
    private func parseElementHidingRule(_ line: String, subscriptionId: UUID) throws -> CompiledFilterRule {
        var ruleType: FilterRuleType = .elementHiding
        var separator = "##"
        
        // 确定分隔符类型
        if line.contains("#@#") {
            separator = "#@#"
            ruleType = .exception
        } else if line.contains("#?#") {
            separator = "#?#"
        }
        
        let components = line.components(separatedBy: separator)
        guard components.count == 2 else {
            throw EasyListParseError.invalidElementHidingRule(line)
        }
        
        let domainPart = components[0]
        let selectorPart = components[1]
        
        var options = FilterRuleOptions()
        
        // 解析域名限制
        if !domainPart.isEmpty {
            let domains = domainPart.components(separatedBy: ",")
            for domain in domains {
                let trimmedDomain = domain.trimmingCharacters(in: .whitespacesAndNewlines)
                if trimmedDomain.hasPrefix("~") {
                    options.excludedDomains.insert(String(trimmedDomain.dropFirst()))
                } else {
                    options.domains.insert(trimmedDomain)
                }
            }
        }
        
        return CompiledFilterRule(
            originalRule: line,
            type: ruleType,
            pattern: selectorPart,
            options: options,
            subscriptionId: subscriptionId
        )
    }
    
    /// 解析网络屏蔽规则
    private func parseNetworkBlockingRule(_ line: String, subscriptionId: UUID) throws -> CompiledFilterRule {
        let (pattern, options) = try parseRuleWithOptions(line)
        
        return CompiledFilterRule(
            originalRule: line,
            type: .networkBlocking,
            pattern: pattern,
            options: options,
            subscriptionId: subscriptionId
        )
    }
    
    /// 解析带选项的规则
    private func parseRuleWithOptions(_ rule: String) throws -> (pattern: String, options: FilterRuleOptions) {
        let components = rule.components(separatedBy: "$")
        let pattern = components[0]
        var options = FilterRuleOptions()
        
        // 解析选项
        if components.count > 1 {
            let optionsString = components[1]
            options = try parseOptions(optionsString)
        }
        
        return (pattern, options)
    }
    
    /// 解析规则选项
    private func parseOptions(_ optionsString: String) throws -> FilterRuleOptions {
        var options = FilterRuleOptions()
        let optionComponents = optionsString.components(separatedBy: ",")
        
        for option in optionComponents {
            let trimmedOption = option.trimmingCharacters(in: .whitespacesAndNewlines)
            
            if trimmedOption.hasPrefix("domain=") {
                let domainString = String(trimmedOption.dropFirst(7))
                let domains = domainString.components(separatedBy: "|")
                
                for domain in domains {
                    if domain.hasPrefix("~") {
                        options.excludedDomains.insert(String(domain.dropFirst()))
                    } else {
                        options.domains.insert(domain)
                    }
                }
            } else if trimmedOption == "third-party" {
                options.thirdParty = true
            } else if trimmedOption == "~third-party" {
                options.thirdParty = false
            } else if trimmedOption == "match-case" {
                options.matchCase = true
            } else if trimmedOption == "important" {
                options.important = true
            } else if trimmedOption.hasPrefix("~") {
                // 排除类型
                options.excludedTypes.insert(String(trimmedOption.dropFirst()))
            } else {
                // 包含类型
                let knownTypes = ["script", "image", "stylesheet", "object", "xmlhttprequest", 
                                "subdocument", "ping", "websocket", "webrtc", "document", 
                                "elemhide", "generichide", "genericblock", "popup", "font", "media", "other"]
                if knownTypes.contains(trimmedOption) {
                    options.types.insert(trimmedOption)
                }
            }
        }
        
        return options
    }
}

// MARK: - 解析错误

enum EasyListParseError: LocalizedError {
    case invalidElementHidingRule(String)
    case invalidNetworkRule(String)
    case invalidOptions(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidElementHidingRule(let rule):
            return "无效的元素隐藏规则: \(rule)"
        case .invalidNetworkRule(let rule):
            return "无效的网络规则: \(rule)"
        case .invalidOptions(let options):
            return "无效的规则选项: \(options)"
        }
    }
}

// MARK: - 规则优化器

extension EasyListParser {
    
    /// 优化规则集合
    func optimizeRules(_ rules: [CompiledFilterRule]) -> [CompiledFilterRule] {
        var optimizedRules: [CompiledFilterRule] = []
        var seenPatterns: Set<String> = []
        
        // 去重
        for rule in rules {
            let key = "\(rule.type.rawValue):\(rule.pattern):\(rule.options.domains.sorted().joined(separator:","))"
            if !seenPatterns.contains(key) {
                seenPatterns.insert(key)
                optimizedRules.append(rule)
            }
        }
        
        logger.info("规则优化: \(rules.count) -> \(optimizedRules.count)")
        return optimizedRules
    }
    
    /// 验证规则有效性
    func validateRule(_ rule: CompiledFilterRule) -> Bool {
        switch rule.type {
        case .networkBlocking, .exception:
            return !rule.pattern.isEmpty
        case .elementHiding:
            return !rule.pattern.isEmpty && isValidCSSSelector(rule.pattern)
        case .comment:
            return true
        }
    }
    
    /// 验证CSS选择器
    private func isValidCSSSelector(_ selector: String) -> Bool {
        // 基本的CSS选择器验证
        let trimmed = selector.trimmingCharacters(in: .whitespacesAndNewlines)
        return !trimmed.isEmpty && !trimmed.contains("javascript:")
    }
}
