import Foundation
import OSLog
import Combine
import UIKit
import WebKit

// MARK: - 统一内存管理器
@MainActor
class UnifiedMemoryManager: ObservableObject {
    static let shared = UnifiedMemoryManager()
    
    // MARK: - 发布的状态
    @Published private(set) var currentMemoryState = MemoryState()
    @Published private(set) var performanceMetrics = MemoryPerformanceMetrics()
    @Published private(set) var memoryHistory: [MemorySnapshot] = []
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.browser", category: "UnifiedMemoryManager")
    private var memoryMonitorTimer: Timer?
    private var memoryPressureSource: DispatchSourceMemoryPressure?
    private var cleanupWorkItem: DispatchWorkItem?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 配置
    private struct Config {
        static let updateInterval: TimeInterval = 5.0
        static let historyLimit = 50 // 减少历史记录数量
        static let cleanupDelay: TimeInterval = 2.0
        static let warningThreshold: Double = 0.75      // 75%触发警告
        static let criticalThreshold: Double = 0.85     // 85%触发紧急
        static let systemReservedMemory: UInt64 = 1024 * 1024 * 1024 // 1GB
        
        // 生产环境日志配置
        #if DEBUG
        static let enableDetailedLogging = true
        static let enablePerformanceLogging = true
        #else
        static let enableDetailedLogging = false
        static let enablePerformanceLogging = false
        #endif
    }
    
    private init() {
        setupMemoryMonitoring()
        logInfo("🧠 统一内存管理器已启动")
    }
    
    deinit {
        // 在deinit中进行简单清理，避免主角色隔离问题
        memoryPressureSource?.cancel()
        memoryMonitorTimer?.invalidate()
        cleanupWorkItem?.cancel()
        cancellables.removeAll()
    }
    
    // MARK: - 核心监控系统
    private func setupMemoryMonitoring() {
        setupSystemMemoryPressureMonitoring()
        setupPeriodicMonitoring()
        setupAppLifecycleObservers()
    }
    
    private func setupSystemMemoryPressureMonitoring() {
        memoryPressureSource = DispatchSource.makeMemoryPressureSource(eventMask: .all)
        memoryPressureSource?.setEventHandler { [weak self] in
            Task { @MainActor in
                await self?.handleSystemMemoryPressure()
            }
        }
        memoryPressureSource?.resume()
    }
    
    private func setupPeriodicMonitoring() {
        memoryMonitorTimer = Timer.scheduledTimer(withTimeInterval: Config.updateInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.updateMemoryState()
            }
        }
    }
    
    private func setupAppLifecycleObservers() {
        NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.handleMemoryWarning()
                }
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.handleBackgroundTransition()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 内存状态更新
    private func updateMemoryState() async {
        let currentUsage = getCurrentAppMemoryUsage()
        let totalMemory = getTotalMemory()
        let availableMemory = totalMemory > currentUsage ? totalMemory - currentUsage : 0
        let utilizationRatio = Double(currentUsage) / Double(totalMemory)
        
        let newPressureLevel = determinePressureLevel(utilizationRatio)
        
        currentMemoryState = MemoryState(
            currentUsage: currentUsage,
            availableMemory: availableMemory,
            totalMemory: totalMemory,
            utilizationRatio: utilizationRatio,
            pressureLevel: newPressureLevel,
            systemReserved: Config.systemReservedMemory,
            cacheUsage: estimateCacheUsage(),
            webViewMemory: estimateWebViewMemory()
        )
        
        // 创建内存快照
        let snapshot = MemorySnapshot(
            timestamp: Date(),
            memoryUsage: currentUsage,
            pressureLevel: newPressureLevel,
            utilizationRatio: utilizationRatio
        )
        
        addMemorySnapshot(snapshot)
        updatePerformanceMetrics()
        
        // 根据压力等级执行相应操作
        await handleMemoryPressureLevel(newPressureLevel)
        
        // 仅在非正常状态时记录日志
        if newPressureLevel != .normal && Config.enablePerformanceLogging {
            logWarning("内存状态: \(formatBytes(currentUsage)) (\(Int(utilizationRatio * 100))%) - \(newPressureLevel.description)")
        }
    }
    
    // MARK: - 内存压力处理
    private func handleSystemMemoryPressure() async {
        let currentLevel = currentMemoryState.pressureLevel
        logWarning("系统内存压力检测，当前级别: \(currentLevel.description)")
        
        await handleMemoryPressureLevel(currentLevel)
    }
    
    private func handleMemoryPressureLevel(_ level: SimplifiedMemoryPressureLevel) async {
        switch level {
        case .normal:
            // 正常状态：执行预防性优化
            await performPreventiveOptimization()
        case .warning:
            // 警告状态：执行基础清理
            await performWarningLevelCleanup()
        case .critical:
            // 紧急状态：执行激进清理
            await performCriticalMemoryRecovery()
        }
    }
    
    private func handleMemoryWarning() async {
        performanceMetrics.memoryWarnings += 1
        logWarning("收到系统内存警告 #\(performanceMetrics.memoryWarnings)")
        
        await performCriticalMemoryRecovery()
    }
    
    private func handleBackgroundTransition() async {
        logInfo("应用进入后台，执行内存优化")
        await performBackgroundOptimization()
    }
    
    // MARK: - 内存清理策略
    private func performPreventiveOptimization() async {
        // 延迟执行，避免频繁清理
        cleanupWorkItem?.cancel()
        cleanupWorkItem = DispatchWorkItem { [weak self] in
            Task { @MainActor in
                await self?.executePreventiveCleanup()
            }
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + Config.cleanupDelay, execute: cleanupWorkItem!)
    }
    
    private func performWarningLevelCleanup() async {
        logInfo("执行警告级内存清理")
        
        await clearImageCache()
        await optimizeWebViewMemory()
        
        performanceMetrics.cleanupCount += 1
        performanceMetrics.lastCleanupTime = Date()
    }
    
    private func performCriticalMemoryRecovery() async {
        logWarning("执行紧急内存恢复")
        
        // 取消延迟任务，立即执行
        cleanupWorkItem?.cancel()
        
        await clearAllCaches()
        await forceWebViewMemoryOptimization()
        await triggerGarbageCollection()
        
        // 通知系统内存警告
        NotificationCenter.default.post(name: UIApplication.didReceiveMemoryWarningNotification, object: nil)
        
        performanceMetrics.cleanupCount += 1
        performanceMetrics.lastCleanupTime = Date()
        
        logInfo("紧急内存恢复完成")
    }
    
    private func performBackgroundOptimization() async {
        await clearImageCache()
        await suspendInactiveWebViews()
        performanceMetrics.backgroundOptimizations += 1
    }
    
    // MARK: - 具体清理操作
    private func executePreventiveCleanup() async {
        // 温和的预防性清理
        if Config.enableDetailedLogging {
            logInfo("执行预防性清理")
        }
    }
    
    private func clearImageCache() async {
        // 清理图片缓存
        URLCache.shared.removeAllCachedResponses()
        
        if Config.enableDetailedLogging {
            logInfo("图片缓存已清理")
        }
    }
    
    private func clearAllCaches() async {
        // 清理所有缓存
        URLCache.shared.removeAllCachedResponses()
        
        let dataStore = WKWebsiteDataStore.default()
        let cacheTypes: Set<String> = [
            WKWebsiteDataTypeMemoryCache,
            WKWebsiteDataTypeDiskCache
        ]
        
        await dataStore.removeData(ofTypes: cacheTypes, modifiedSince: .distantPast)
        
        if Config.enableDetailedLogging {
            logInfo("所有缓存已清理")
        }
    }
    
    private func optimizeWebViewMemory() async {
        // 委托给BrowserManager处理WebView内存优化
        // 避免重复的内存管理操作
        await BrowserManager.shared.optimizeMemory()
    }

    private func forceWebViewMemoryOptimization() async {
        // 委托给BrowserManager处理强制WebView内存优化
        // 确保统一的内存管理策略
        await BrowserManager.shared.optimizeMemory()
    }

    private func suspendInactiveWebViews() async {
        // 通知BrowserManager挂起非活跃WebViews
        // 通过通知模式实现解耦
        NotificationCenter.default.post(name: Notification.Name("SuspendInactiveWebViews"), object: nil)
    }
    
    private func triggerGarbageCollection() async {
        // 强制垃圾回收
        await Task.yield()
    }
    
    // MARK: - 辅助方法
    private func determinePressureLevel(_ utilizationRatio: Double) -> SimplifiedMemoryPressureLevel {
        if utilizationRatio >= Config.criticalThreshold {
            return .critical
        } else if utilizationRatio >= Config.warningThreshold {
            return .warning
        } else {
            return .normal
        }
    }
    
    private func addMemorySnapshot(_ snapshot: MemorySnapshot) {
        memoryHistory.append(snapshot)
        
        // 保持历史记录在合理范围内
        if memoryHistory.count > Config.historyLimit {
            memoryHistory.removeFirst()
        }
    }
    
    private func updatePerformanceMetrics() {
        let currentUtilization = currentMemoryState.utilizationRatio
        
        // 更新平均使用率
        performanceMetrics.averageUtilization = (performanceMetrics.averageUtilization * 0.9) + (currentUtilization * 0.1)
        
        // 更新峰值使用率
        if currentUtilization > performanceMetrics.peakUtilization {
            performanceMetrics.peakUtilization = currentUtilization
        }
    }
    
    // MARK: - 内存信息获取
    private func getCurrentAppMemoryUsage() -> UInt64 {
        var taskInfo = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &taskInfo) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        return kerr == KERN_SUCCESS ? taskInfo.resident_size : 0
    }
    
    private func getTotalMemory() -> UInt64 {
        return ProcessInfo.processInfo.physicalMemory
    }
    
    private func estimateCacheUsage() -> UInt64 {
        return UInt64(URLCache.shared.currentMemoryUsage + URLCache.shared.currentDiskUsage)
    }
    
    private func estimateWebViewMemory() -> UInt64 {
        // 简化的WebView内存估算
        return 64 * 1024 * 1024 // 64MB基础估算
    }
    
    private func formatBytes(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useGB, .useMB]
        formatter.countStyle = .decimal
        return formatter.string(fromByteCount: Int64(bytes))
    }
    
    // MARK: - 日志方法
    private func logInfo(_ message: String) {
        if Config.enableDetailedLogging {
            logger.info("ℹ️ [UnifiedMemoryManager] \(message)")
        }
    }
    
    private func logWarning(_ message: String) {
        logger.warning("⚠️ [UnifiedMemoryManager] \(message)")
    }
    
    private func logError(_ message: String) {
        logger.error("❌ [UnifiedMemoryManager] \(message)")
    }
    
    // MARK: - 公共接口
    func getCurrentMemoryUsage() -> UInt64 {
        return currentMemoryState.currentUsage
    }
    
    func isMemoryUnderPressure() -> Bool {
        return currentMemoryState.pressureLevel != .normal
    }
    
    func getPressureLevel() -> SimplifiedMemoryPressureLevel {
        return currentMemoryState.pressureLevel
    }
    
    func forceMemoryCleanup() async {
        logInfo("手动触发内存清理")
        await performCriticalMemoryRecovery()
    }
    
    // MARK: - 清理
    private func cleanup() {
        memoryPressureSource?.cancel()
        memoryMonitorTimer?.invalidate()
        cleanupWorkItem?.cancel()
        cancellables.removeAll()
    }
    
    // MARK: - 调试报告
    var optimizationReport: String {
        let uptime = Date().timeIntervalSince(performanceMetrics.startTime)
        
        return """
        🧠 统一内存管理器报告
        ==========================================
        
        📊 当前状态:
        - 内存使用: \(formatBytes(currentMemoryState.currentUsage))
        - 使用率: \(Int(currentMemoryState.utilizationRatio * 100))%
        - 压力等级: \(currentMemoryState.pressureLevel.description)
        
        📈 性能指标:
        - 运行时间: \(Int(uptime / 3600))小时 \(Int((uptime.truncatingRemainder(dividingBy: 3600)) / 60))分钟
        - 平均使用率: \(Int(performanceMetrics.averageUtilization * 100))%
        - 峰值使用率: \(Int(performanceMetrics.peakUtilization * 100))%
        - 清理次数: \(performanceMetrics.cleanupCount)
        - 内存警告: \(performanceMetrics.memoryWarnings)次
        - 后台优化: \(performanceMetrics.backgroundOptimizations)次
        
        📊 历史记录: \(memoryHistory.count)个快照
        """
    }
}

// MARK: - 数据模型

// 简化的内存压力等级（从4级简化为3级）
enum SimplifiedMemoryPressureLevel: CustomStringConvertible {
    case normal   // 正常（0-75%）
    case warning  // 警告（75-85%）
    case critical // 紧急（85%+）
    
    var description: String {
        switch self {
        case .normal: return "正常"
        case .warning: return "警告"
        case .critical: return "紧急"
        }
    }
}

struct MemoryState {
    var currentUsage: UInt64 = 0
    var availableMemory: UInt64 = 0
    var totalMemory: UInt64 = 0
    var utilizationRatio: Double = 0.0
    var pressureLevel: SimplifiedMemoryPressureLevel = .normal
    var systemReserved: UInt64 = 0
    var cacheUsage: UInt64 = 0
    var webViewMemory: UInt64 = 0
    
    init() {}
    
    init(currentUsage: UInt64, availableMemory: UInt64, totalMemory: UInt64, utilizationRatio: Double, pressureLevel: SimplifiedMemoryPressureLevel, systemReserved: UInt64, cacheUsage: UInt64, webViewMemory: UInt64) {
        self.currentUsage = currentUsage
        self.availableMemory = availableMemory
        self.totalMemory = totalMemory
        self.utilizationRatio = utilizationRatio
        self.pressureLevel = pressureLevel
        self.systemReserved = systemReserved
        self.cacheUsage = cacheUsage
        self.webViewMemory = webViewMemory
    }
}

struct MemoryPerformanceMetrics {
    var averageUtilization: Double = 0.0
    var peakUtilization: Double = 0.0
    var cleanupCount: Int = 0
    var lastCleanupTime: Date = Date()
    var memoryWarnings: Int = 0
    var backgroundOptimizations: Int = 0
    var startTime: Date = Date()
}

struct MemorySnapshot {
    let timestamp: Date
    let memoryUsage: UInt64
    let pressureLevel: SimplifiedMemoryPressureLevel
    let utilizationRatio: Double
    
    var formattedUsage: String {
        return ByteCountFormatter.string(fromByteCount: Int64(memoryUsage), countStyle: .memory)
    }
    
    var formattedUtilization: String {
        return "\(Int(utilizationRatio * 100))%"
    }
} 