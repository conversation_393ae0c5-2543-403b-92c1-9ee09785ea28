<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>广告屏蔽测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-weight: bold;
            color: #007AFF;
            margin-bottom: 10px;
        }
        .should-be-blocked {
            background-color: #ffebee;
            border: 2px solid #f44336;
            color: #d32f2f;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .normal-content {
            background-color: #e8f5e8;
            border: 2px solid #4caf50;
            color: #2e7d32;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        /* 模拟广告样式 */
        .ad, .ads, .advertisement, .ad-banner, .ad-container {
            background-color: #ff9800;
            color: white;
            padding: 20px;
            text-align: center;
            margin: 10px 0;
            border-radius: 5px;
        }
        .sponsored {
            background-color: #9c27b0;
            color: white;
            padding: 15px;
            text-align: center;
            margin: 10px 0;
            border-radius: 5px;
        }
        [data-ad] {
            background-color: #f44336;
            color: white;
            padding: 15px;
            text-align: center;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007AFF;
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="status" id="adBlockStatus">检测中...</div>
    
    <div class="container">
        <div class="header">
            <h1>🛡️ 广告屏蔽功能测试</h1>
            <p>此页面用于测试浏览器的广告屏蔽功能是否正常工作</p>
        </div>

        <div class="test-section">
            <div class="test-title">测试1: CSS类名广告屏蔽</div>
            <div class="normal-content">✅ 这是正常内容，应该显示</div>
            <div class="ad should-be-blocked">❌ 这是 .ad 类的广告，应该被屏蔽</div>
            <div class="ads should-be-blocked">❌ 这是 .ads 类的广告，应该被屏蔽</div>
            <div class="advertisement should-be-blocked">❌ 这是 .advertisement 类的广告，应该被屏蔽</div>
        </div>

        <div class="test-section">
            <div class="test-title">测试2: ID属性广告屏蔽</div>
            <div class="normal-content">✅ 这是正常内容，应该显示</div>
            <div id="ad-banner" class="should-be-blocked">❌ 这是 #ad-banner ID的广告，应该被屏蔽</div>
            <div id="ads-container" class="should-be-blocked">❌ 这是 #ads-container ID的广告，应该被屏蔽</div>
        </div>

        <div class="test-section">
            <div class="test-title">测试3: 数据属性广告屏蔽</div>
            <div class="normal-content">✅ 这是正常内容，应该显示</div>
            <div data-ad="banner" class="should-be-blocked">❌ 这是 data-ad 属性的广告，应该被屏蔽</div>
            <div data-ad-slot="123" class="should-be-blocked">❌ 这是 data-ad-slot 属性的广告，应该被屏蔽</div>
        </div>

        <div class="test-section">
            <div class="test-title">测试4: 赞助内容屏蔽</div>
            <div class="normal-content">✅ 这是正常内容，应该显示</div>
            <div class="sponsored should-be-blocked">❌ 这是赞助内容，应该被屏蔽</div>
        </div>

        <div class="test-section">
            <div class="test-title">测试5: 动态广告元素</div>
            <div class="normal-content">✅ 这是正常内容，应该显示</div>
            <div id="dynamic-ads"></div>
            <button onclick="addDynamicAd()">添加动态广告</button>
        </div>

        <div class="test-section">
            <div class="test-title">测试结果统计</div>
            <div id="testResults">
                <p>正在检测广告屏蔽效果...</p>
            </div>
        </div>
    </div>

    <script>
        // 检测广告屏蔽效果
        function checkAdBlockStatus() {
            const adElements = document.querySelectorAll('.should-be-blocked');
            let blockedCount = 0;
            let totalCount = adElements.length;
            
            adElements.forEach(element => {
                const style = window.getComputedStyle(element);
                if (style.display === 'none' || 
                    style.visibility === 'hidden' || 
                    style.opacity === '0' ||
                    style.height === '0px' ||
                    style.width === '0px') {
                    blockedCount++;
                }
            });
            
            const blockRate = totalCount > 0 ? (blockedCount / totalCount * 100).toFixed(1) : 0;
            
            // 更新状态显示
            const statusElement = document.getElementById('adBlockStatus');
            if (blockedCount === totalCount && totalCount > 0) {
                statusElement.textContent = '🛡️ 广告屏蔽正常';
                statusElement.style.backgroundColor = '#4caf50';
            } else if (blockedCount > 0) {
                statusElement.textContent = `⚠️ 部分屏蔽 ${blockRate}%`;
                statusElement.style.backgroundColor = '#ff9800';
            } else {
                statusElement.textContent = '❌ 广告屏蔽未生效';
                statusElement.style.backgroundColor = '#f44336';
            }
            
            // 更新测试结果
            const resultsElement = document.getElementById('testResults');
            resultsElement.innerHTML = `
                <p><strong>测试结果:</strong></p>
                <p>总广告元素: ${totalCount}</p>
                <p>已屏蔽: ${blockedCount}</p>
                <p>屏蔽率: ${blockRate}%</p>
                <p>状态: ${blockedCount === totalCount ? '✅ 完全屏蔽' : blockedCount > 0 ? '⚠️ 部分屏蔽' : '❌ 未屏蔽'}</p>
            `;
        }
        
        // 添加动态广告
        function addDynamicAd() {
            const container = document.getElementById('dynamic-ads');
            const adElement = document.createElement('div');
            adElement.className = 'ad should-be-blocked';
            adElement.textContent = '❌ 这是动态添加的广告，应该被屏蔽';
            container.appendChild(adElement);
            
            // 延迟检测，给广告屏蔽脚本时间处理
            setTimeout(checkAdBlockStatus, 500);
        }
        
        // 页面加载完成后检测
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟检测，确保广告屏蔽脚本已执行
            setTimeout(checkAdBlockStatus, 1000);
            
            // 定期检测
            setInterval(checkAdBlockStatus, 3000);
        });
        
        // 模拟一些可能被屏蔽的网络请求
        setTimeout(() => {
            // 这些请求应该被广告屏蔽脚本拦截
            fetch('https://doubleclick.net/test').catch(() => {
                console.log('✅ doubleclick.net 请求被屏蔽');
            });
            
            fetch('https://googleadservices.com/test').catch(() => {
                console.log('✅ googleadservices.com 请求被屏蔽');
            });
            
            fetch('/ads/banner.js').catch(() => {
                console.log('✅ /ads/ 路径请求被屏蔽');
            });
        }, 2000);
    </script>
</body>
</html>
