//
//  NewWebBrowserViewModel.swift
//  cop
//
//  Created by Augment Agent on 2025/6/2.
//

import Foundation
import SwiftUI
import WebKit
import Combine

// MARK: - 简化的标签页性能指标
struct TabPerformanceMetrics {
    var loadCount: Int = 0
    var errorCount: Int = 0
    var lastLoadTime: Double = 0
    
    mutating func recordLoad(time: Double) {
        lastLoadTime = time
        loadCount += 1
    }
    
    mutating func recordError() {
        errorCount += 1
    }
    
    // 简化的性能评估
    var performanceLevel: String {
        if errorCount > 3 {
            return "需要注意"
        } else if loadCount > 10 {
            return "活跃"
        } else {
            return "正常"
        }
    }
}

// MARK: - 优化后的浏览器标签页模型
class NewBrowserTab: ObservableObject, Identifiable, Equatable {
    let id: UUID

    @Published var title: String = "新标签页"
    @Published var url: URL?
    @Published var isLoading: Bool = false
    @Published var canGoBack: Bool = false
    @Published var canGoForward: Bool = false
    @Published var estimatedProgress: Double = 0.0
    @Published var lastActiveDate: Date = Date()
    @Published var isSuspended: Bool = false // 新增：标签页挂起状态
    // 暂时移除快照功能以简化代码
    // @Published var snapshot: Image?
    @Published var memoryUsage: UInt64 = 0

    // MARK: - 状态保存（用于恢复挂起的标签页）
    private var savedTitle: String?
    private var savedURL: URL?
    private var savedCanGoBack = false
    private var savedCanGoForward = false

    // WebView 实例管理 - 使用优化的统一管理器
    private var _webView: WKWebView?
    
    // 性能监控
    var loadingStartTime: Date?
    var performanceMetrics: TabPerformanceMetrics = TabPerformanceMetrics()

    init(id: UUID = UUID(), url: URL? = nil) {
        self.id = id
        self.url = url
        self.lastActiveDate = Date()
        if let url = url {
            self.title = url.host ?? "新标签页"
        }
    }

    deinit {
        #if DEBUG
        print("🧹 [NewBrowserTab] 清理标签页: \(title.isEmpty ? "未命名" : title)")
        #endif

        // 简化的WebView清理
        if let webView = _webView {
            DispatchQueue.main.async {
                webView.stopLoading()
                webView.removeFromSuperview()
            }
        }
    }

    // MARK: - WebView管理（优化版本）
    var webView: WKWebView? {
        get {
            return _webView
        }
        set {
            if let oldWebView = _webView, oldWebView != newValue {
                // 简化的WebView清理
                oldWebView.stopLoading()
                oldWebView.removeFromSuperview()
            }
            _webView = newValue
        }
    }
    
    // 获取或创建WebView（简化版本）
    @MainActor
    func getWebView(userAgent: String) -> WKWebView {
        if let existingWebView = _webView {
            existingWebView.customUserAgent = userAgent
            return existingWebView
        }

        // 创建新的WebView配置
        let config = WKWebViewConfiguration()
        config.preferences.javaScriptCanOpenWindowsAutomatically = false
        config.mediaTypesRequiringUserActionForPlayback = [.video, .audio]

        // 创建WebView
        let webView = WKWebView(frame: .zero, configuration: config)
        webView.customUserAgent = userAgent
        webView.allowsLinkPreview = true
        webView.allowsBackForwardNavigationGestures = true

        _webView = webView
        return webView
    }

    // 清理WebView资源（简化版本）
    @MainActor
    func cleanup() {
        print("🧹 [NewBrowserTab] 清理标签页资源: \(id)")

        // 简化的WebView清理
        if let webView = _webView {
            webView.stopLoading()
            webView.removeFromSuperview()
            _webView = nil
        }

        // 清理其他资源
        // snapshot = nil // 暂时移除快照功能
        performanceMetrics = TabPerformanceMetrics()
        isSuspended = false
    }
    
    // 标记为活跃状态（用户正在查看）
    @MainActor
    func markAsActive() {
        lastActiveDate = Date()
        print("🎯 [NewBrowserTab] 标签页标记为活跃: \(id)")
    }
    
    // 更新标签页状态
    func updateState(title: String? = nil, url: URL? = nil, isLoading: Bool? = nil, 
                    canGoBack: Bool? = nil, canGoForward: Bool? = nil, estimatedProgress: Double? = nil) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            if let title = title { self.title = title }
            if let url = url { self.url = url }
            if let isLoading = isLoading { self.isLoading = isLoading }
            if let canGoBack = canGoBack { self.canGoBack = canGoBack }
            if let canGoForward = canGoForward { self.canGoForward = canGoForward }
            if let estimatedProgress = estimatedProgress { self.estimatedProgress = estimatedProgress }
            
            // 更新最后活跃时间
            self.lastActiveDate = Date()
        }
    }
    
    // 拍摄WebView快照 - 暂时禁用
    @MainActor
    func takeSnapshot() async {
        // 暂时移除快照功能以简化代码
        print("📸 [NewBrowserTab] 快照功能已暂时禁用")
    }
    
    // 加载URL（简化版本）
    @MainActor
    func loadURL(_ url: URL, userAgent: String) {
        // 如果标签页被挂起，先恢复
        if isSuspended {
            restoreFromSuspension()
        }

        // 确保WebView存在
        let webView = getWebView(userAgent: userAgent)

        // 直接加载URL
        let request = URLRequest(url: url)
        webView.load(request)

        // 更新标签页URL
        self.url = url
    }
    
    // 加载HTML字符串
    @MainActor
    func loadHTMLString(_ string: String, baseURL: URL?, userAgent: String) {
        let webView = getWebView(userAgent: userAgent)
        webView.loadHTMLString(string, baseURL: baseURL)
    }

    // MARK: - 标签页挂起和恢复功能
    @MainActor
    func suspendTab() {
        guard !isSuspended, let webView = _webView else { return }

        // 保存当前状态
        savedTitle = webView.title
        savedURL = webView.url
        savedCanGoBack = webView.canGoBack
        savedCanGoForward = webView.canGoForward

        // 清理WebView但保留状态
        webView.stopLoading()
        webView.removeFromSuperview()
        _webView = nil

        // 标记为挂起状态
        isSuspended = true

        print("💤 [NewBrowserTab] 标签页已挂起: \(savedTitle ?? "未知")")
    }

    @MainActor
    func restoreFromSuspension() {
        guard isSuspended else { return }

        // 恢复状态
        if let savedTitle = savedTitle {
            title = savedTitle
        }
        if let savedURL = savedURL {
            url = savedURL
        }
        canGoBack = savedCanGoBack
        canGoForward = savedCanGoForward

        // 清除挂起状态
        isSuspended = false

        print("🔄 [NewBrowserTab] 标签页已从挂起状态恢复: \(title)")
    }

    // MARK: - Equatable
    static func == (lhs: NewBrowserTab, rhs: NewBrowserTab) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - 用户代理类型
enum UserAgentType: String, CaseIterable {
    case windowsPC = "windowsPC"
    case macPC = "macPC"
    case android = "android"
    case ios = "ios"
    
    var displayName: String {
        switch self {
        case .windowsPC: return "Windows PC"
        case .macPC: return "Mac PC"
        case .android: return "Android"
        case .ios: return "iOS"
        }
    }
    
    var userAgentString: String {
        switch self {
        case .windowsPC:
            return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"
        case .macPC:
            return "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"
        case .android:
            return "Mozilla/5.0 (Linux; Android 14; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Mobile Safari/537.36"
        case .ios:
            return "Mozilla/5.0 (iPhone; CPU OS 18_4 like Mac OS X) AppleWebKit/615.1.26 (KHTML, like Gecko) Version/18.0 Mobile/15E148 Safari/604.1"
        }
    }
}

// MARK: - 搜索引擎类型
enum BrowserSearchEngine: String, CaseIterable {
    case google = "google"
    case bing = "bing"
    case duckduckgo = "duckduckgo"
    case baidu = "baidu"
    case yahoo = "yahoo"
    case yandex = "yandex"

    var displayName: String {
        switch self {
        case .google: return "Google"
        case .bing: return "Bing"
        case .duckduckgo: return "DuckDuckGo"
        case .baidu: return "百度"
        case .yahoo: return "Yahoo"
        case .yandex: return "Yandex"
        }
    }

    var icon: String {
        switch self {
        case .google: return "g.circle.fill"
        case .bing: return "b.circle.fill"
        case .duckduckgo: return "d.circle.fill"
        case .baidu: return "b.square.fill"
        case .yahoo: return "y.circle.fill"
        case .yandex: return "y.square.fill"
        }
    }

    var searchTemplate: String {
        switch self {
        case .google: return "https://www.google.com/search?q="
        case .bing: return "https://www.bing.com/search?q="
        case .duckduckgo: return "https://duckduckgo.com/?q="
        case .baidu: return "https://www.baidu.com/s?wd="
        case .yahoo: return "https://search.yahoo.com/search?p="
        case .yandex: return "https://yandex.com/search/?text="
        }
    }

    var description: String {
        switch self {
        case .google: return "全球最受欢迎的搜索引擎"
        case .bing: return "微软的搜索引擎"
        case .duckduckgo: return "注重隐私保护的搜索引擎"
        case .baidu: return "中文搜索引擎"
        case .yahoo: return "经典的搜索引擎"
        case .yandex: return "俄罗斯搜索引擎"
        }
    }
}

// MARK: - 优化后的浏览器视图模型
class NewWebBrowserViewModel: ObservableObject {
    
    // MARK: - 标签页管理
    @Published var tabs: [NewBrowserTab] = []
    @Published var currentTabIndex: Int = 0
    
    // MARK: - 标签页管理设置（iPad mini A17 Pro 8GB RAM优化）
    private let maxTabsCount = 12 // 从8个增加到12个，充分利用8GB内存

    // MARK: - 内存监控（简化版本）
    @Published var memoryUsage: UInt64 = 0

    // MARK: - 优化配置（针对iPad mini A17 Pro优化）
    private struct OptimizationSettings {
        static let backgroundTabSuspendDelay: TimeInterval = 45.0 // 从30秒增加到45秒，减少频繁挂起
        static let maxActiveWebViews = 6 // 从4个增加到6个，更好利用8GB内存
        static let memoryPressureThreshold: UInt64 = 400 * 1024 * 1024 // 从200MB增加到400MB，适应8GB设备
        static let enableAggressiveOptimization = false // 关闭激进优化，减少不必要的性能开销
        static let tabSuspendMemoryThreshold: UInt64 = 600 * 1024 * 1024 // 新增：600MB时开始挂起后台标签页
    }

    // MARK: - 定时器管理
    private var backgroundSuspendTimer: Timer?
    private var performanceCheckTimer: Timer?
    private var stateSaveTimer: Timer?



    // MARK: - 浏览器设置
    @Published var currentUserAgent: UserAgentType = .ios // 默认iOS代理
    @Published var currentSearchEngine: BrowserSearchEngine = .google
    @Published var isHTTPSOnlyEnabled: Bool = true // 简化的HTTPS设置

    // MARK: - 搜索引擎
    var searchEngine: String {
        return currentSearchEngine.displayName
    }

    var searchQueryTemplate: String {
        return currentSearchEngine.searchTemplate
    }
    
    // MARK: - 历史记录和书签
    @Published var history: [HistoryItem] = []
    @Published var bookmarks: [BookmarkItem] = []
    
    // MARK: - 缓存管理 (已移至 DataManagementView 统一管理)
    
    private var userDefaults: UserDefaults
    
    // MARK: - 计算属性
    var currentTab: NewBrowserTab? {
        guard currentTabIndex >= 0 && currentTabIndex < tabs.count else { return nil }
        return tabs[currentTabIndex]
    }
    
    var effectiveUserAgent: String {
        return currentUserAgent.userAgentString
    }
    
    init() {
        // 初始化存储属性
        self.userDefaults = UserDefaults.standard

        // 现在可以安全地调用方法
        loadSettings()
        loadTabs()
        loadHistory()
        loadBookmarks()
        rebuildBookmarkURLSet()

        // 初始化标签页
        if tabs.isEmpty {
            createInitialTab()
        }

        // 确保当前标签页索引有效
        ensureValidCurrentTabIndex()

        // 启动内存监控
        startMemoryMonitoring()

        print("✅ [NewWebBrowserViewModel] 初始化完成，标签页数量: \(tabs.count)")
    }
    
    deinit {
        // 清理资源
        backgroundSuspendTimer?.invalidate()
        performanceCheckTimer?.invalidate()
        stateSaveTimer?.invalidate()

        // 注意：在deinit中不使用async Task，避免Swift 6错误
        print("🧹 [NewWebBrowserViewModel] deinit 开始清理")
    }

    // MARK: - 简化的内存监控
    private func startMemoryMonitoring() {
        // 启动内存检查定时器
        performanceCheckTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateMemoryUsage()
                await self?.performPerformanceCheck()
            }
        }
    }

    private func updateMemoryUsage() {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }

        if kerr == KERN_SUCCESS {
            memoryUsage = UInt64(info.resident_size)
        }
    }

    // MARK: - 优化系统初始化
    private func setupOptimizationSystem() {
        print("🚀 [NewWebBrowserViewModel] 初始化优化系统")
        
        // 启动性能检查定时器
        performanceCheckTimer = Timer.scheduledTimer(withTimeInterval: 15.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performPerformanceCheck()
            }
        }
        
        // 启动后台标签页挂起定时器
        setupBackgroundTabSuspension()
        
        print("✅ [NewWebBrowserViewModel] 优化系统初始化完成")
    }
    
    private func setupBackgroundTabSuspension() {
        backgroundSuspendTimer = Timer.scheduledTimer(withTimeInterval: OptimizationSettings.backgroundTabSuspendDelay, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.cleanupInactiveBackgroundTabs()
            }
        }
    }
    
    // MARK: - 简化的内存管理
    private func setupAdvancedMemoryManagement() {
        // 简化版本：移除复杂的应用生命周期监听
        print("🔧 [NewWebBrowserViewModel] 内存管理已简化")
    }
    
    @MainActor
    private func handleCriticalMemoryWarning() async {
        print("🚨 [NewWebBrowserViewModel] 收到严重内存警告，执行紧急清理")

        // 1. 清理所有非活跃标签页
        await cleanupInactiveBackgroundTabs()

        // 2. 清理历史记录和缓存
        if history.count > 100 {
            history = Array(history.suffix(50))
            print("🧹 清理历史记录：保留最近50条")
        }

        // 3. 清理快照 - 暂时禁用
        // for tab in tabs where tab.id != currentTab?.id {
        //     tab.snapshot = nil
        // }

        // 4. 触发垃圾回收
        await performGarbageCollection()

        print("✅ [NewWebBrowserViewModel] 紧急清理完成")
    }
    
    @MainActor
    private func handleAppEnterBackground() async {
        print("📱 [NewWebBrowserViewModel] 应用进入后台，暂停非必要服务")

        // 暂停性能监控
        performanceCheckTimer?.invalidate()

        // 清理后台标签页
        await cleanupInactiveBackgroundTabs()

        // 保存当前状态
        saveTabs()
    }

    @MainActor
    private func handleAppEnterForeground() {
        print("📱 [NewWebBrowserViewModel] 应用回到前台，恢复服务")

        // 重启性能监控
        startMemoryMonitoring()

        // 确保当前标签页处于活跃状态
        currentTab?.markAsActive()
    }

    
    // MARK: - 性能检查（iPad mini A17 Pro优化）
    @MainActor
    private func performPerformanceCheck() async {
        // 分层内存检查 - 更智能的内存管理
        if memoryUsage > OptimizationSettings.tabSuspendMemoryThreshold {
            #if DEBUG
            print("🔶 [NewWebBrowserViewModel] 内存使用达到标签页挂起阈值: \(ByteCountFormatter.string(fromByteCount: Int64(memoryUsage), countStyle: .memory))，开始挂起后台标签页")
            #endif
            await suspendBackgroundTabs()
        } else if memoryUsage > OptimizationSettings.memoryPressureThreshold {
            #if DEBUG
            print("⚠️ [NewWebBrowserViewModel] 内存使用: \(ByteCountFormatter.string(fromByteCount: Int64(memoryUsage), countStyle: .memory))，开始轻度优化")
            #endif
            await handleMemoryPressure()
        }

        // 检查活跃WebView数量 - 使用新的限制
        let activeWebViewCount = tabs.filter { $0.webView != nil }.count
        if activeWebViewCount > OptimizationSettings.maxActiveWebViews {
            print("📊 [NewWebBrowserViewModel] 活跃WebView数量(\(activeWebViewCount))超过限制(\(OptimizationSettings.maxActiveWebViews))，开始清理")
            await cleanupInactiveBackgroundTabs()
        }
        
        // 检查加载性能 - 使用简化的性能评估
        // 简化的性能检查，不依赖复杂的指标
        let activeTabsCount = tabs.filter { $0.webView != nil }.count
        if activeTabsCount > OptimizationSettings.maxActiveWebViews {
            print("🐌 [NewWebBrowserViewModel] 加载性能下降，应用优化")
            optimizeLoadingPerformance()
        }
    }
    
    // MARK: - 简化的标签页管理
    @MainActor
    private func cleanupInactiveBackgroundTabs() async {
        // 简化版本：只清理非当前标签页的WebView
        for tab in tabs where tab.id != currentTab?.id {
            tab.cleanup()
        }
    }
    
    // MARK: - 优化的内存管理策略（iPad mini A17 Pro）
    @MainActor
    private func handleMemoryPressure() async {
        print("💾 [NewWebBrowserViewModel] 执行轻度内存优化策略")

        // 1. 清理非活跃WebView
        await cleanupInactiveBackgroundTabs()

        // 2. 更宽松的标签页数量限制 - 适应8GB内存
        if tabs.count > 8 {
            closeOldestInactiveTabs(keepCount: 6)
        }

        // 3. 清理缓存
        clearNonEssentialCaches()
    }
    
    // MARK: - 简化的内存优化
    @MainActor
    private func handleMemoryOptimizationRequest() async {
        print("🔧 [NewWebBrowserViewModel] 收到内存优化请求")
        await cleanupInactiveBackgroundTabs()
        clearNonEssentialCaches()
    }

    @MainActor
    private func handleCriticalMemoryPressure() async {
        print("🚨 [NewWebBrowserViewModel] 收到紧急内存压力信号")
        await handleMemoryPressure()

        // 强制清理所有WebView缓存
        for tab in tabs {
            if let webView = tab.webView {
                Task {
                    await webView.configuration.websiteDataStore.removeData(
                        ofTypes: [WKWebsiteDataTypeMemoryCache],
                        modifiedSince: .distantPast
                    )
                }
            }
        }
    }
    
    @MainActor
    private func optimizeLoadingPerformance() {
        // 简化版本：基本的性能优化
        print("⚡ [NewWebBrowserViewModel] 应用基础性能优化")
    }
    
    @MainActor
    private func closeOldestInactiveTabs(keepCount: Int = 8) {
        guard tabs.count > keepCount else { return }

        // 获取非当前标签页
        let inactiveTabs = tabs.filter { $0.id != currentTab?.id }

        // 按最后活跃时间排序
        let sortedInactiveTabs = inactiveTabs.sorted { $0.lastActiveDate < $1.lastActiveDate }

        // 关闭最旧的标签页，但保持更多标签页开放
        let closeCount = tabs.count - keepCount
        for i in 0..<min(closeCount, sortedInactiveTabs.count) {
            let tab = sortedInactiveTabs[i]
            if let index = tabs.firstIndex(where: { $0.id == tab.id }) {
                closeTab(at: index)
            }
        }

        print("🗑️ [NewWebBrowserViewModel] 关闭了 \(min(closeCount, sortedInactiveTabs.count)) 个旧标签页，保留 \(keepCount) 个")
    }
    
    @MainActor
    private func clearNonEssentialCaches() {
        // 清理网站数据（保留cookie和本地存储）
        let dataStore = WKWebsiteDataStore.default()
        let dataTypes: Set<String> = [
            WKWebsiteDataTypeMemoryCache,
            WKWebsiteDataTypeDiskCache
        ]
        
        dataStore.removeData(
            ofTypes: dataTypes,
            modifiedSince: Date(timeIntervalSinceNow: -3600), // 清理1小时内的缓存
            completionHandler: {
                print("🧹 [NewWebBrowserViewModel] 已清理非必要缓存")
            }
        )
    }
    
    @MainActor
    private func performGarbageCollection() async {
        // 简化版本：基本的垃圾回收
        print("🗑️ [NewWebBrowserViewModel] 执行垃圾回收")
    }
    
    // MARK: - WebView配置优化（使用统一配置管理器）
    // 配置方法已移至BrowserManager中统一管理
    
    // MARK: - 调试和监控工具
    @MainActor
    func printOptimizationStatus() {
        print("""
        🔍 [NewWebBrowserViewModel] 综合优化状态报告
        ============================================
        
        📊 标签页状态:
        - 总标签页数: \(tabs.count)
        - 活跃WebView数: \(tabs.filter { $0.webView != nil }.count)
        
        💾 内存状态:
        - 当前内存使用: \(ByteCountFormatter.string(fromByteCount: Int64(memoryUsage), countStyle: .memory))
        - HTTPS强制: \(isHTTPSOnlyEnabled ? "已启用" : "已禁用")

        ⚡ 性能指标:
        - 活跃WebView数: \(tabs.filter { $0.webView != nil && !$0.isSuspended }.count)
        - 挂起标签页数: \(tabs.filter { $0.isSuspended }.count)

        🌐 浏览器状态:
        轻量化浏览器运行正常
        
        🎨 用户体验状态:
        自适应界面已启用
        """)
    }
        
    // MARK: - 缓存清理
    private func clearWebKitCache() {
        let dataStore = WKWebsiteDataStore.default()
        let cacheTypes: Set<String> = [
            WKWebsiteDataTypeMemoryCache,
            WKWebsiteDataTypeDiskCache
        ]
        
        dataStore.removeData(ofTypes: cacheTypes, modifiedSince: .distantPast) {
            print("WebKit缓存已清理")
        }
    }

    // MARK: - 简化的应用生命周期管理
    private func setupAppLifecycleObservers() {
        // 简化版本：移除复杂的应用生命周期监听
        print("🔧 [NewWebBrowserViewModel] 应用生命周期管理已简化")

        // 添加定时保存机制
        stateSaveTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.saveAppState()
            }
        }
    }
    
    // MARK: - 智能后台标签页挂起（新增）
    @MainActor
    private func suspendBackgroundTabs() async {
        print("💤 [NewWebBrowserViewModel] 智能挂起后台标签页以释放内存")

        let backgroundTabs = tabs.enumerated().compactMap { index, tab in
            index != currentTabIndex ? tab : nil
        }

        // 按最后活跃时间排序，挂起最久未使用的标签页
        let sortedTabs = backgroundTabs.sorted { $0.lastActiveDate < $1.lastActiveDate }

        // 智能挂起策略：根据内存使用情况决定挂起数量
        let tabsToSuspendCount: Int

        if memoryUsage > OptimizationSettings.tabSuspendMemoryThreshold {
            // 高内存使用：挂起更多标签页
            tabsToSuspendCount = min(sortedTabs.count * 2 / 3, sortedTabs.count)
        } else {
            // 中等内存使用：挂起一半标签页
            tabsToSuspendCount = min(sortedTabs.count / 2, sortedTabs.count)
        }

        for tab in sortedTabs.prefix(tabsToSuspendCount) {
            if !tab.isSuspended {
                print("💤 挂起标签页: \(tab.title)")
                tab.suspendTab()
            }
        }

        print("💤 已挂起 \(tabsToSuspendCount) 个后台标签页")
    }

    @MainActor
    private func saveAppState() {
        #if DEBUG
        print("🔄 开始保存应用状态...")
        #endif
        
        // 更新当前标签页的URL（如果WebView已加载）
        updateCurrentTabURL()

        // 保存所有状态
        saveTabs()
        saveSettings()
        saveHistory()
        saveBookmarks()
        
        // 同步UserDefaults
        userDefaults.synchronize()
        
        #if DEBUG
        print("✅ 应用状态保存完成")
        #endif
    }

    private func restoreAppState() {
        #if DEBUG
        print("🔄 开始恢复应用状态...")
        #endif
        
        // 重新加载设置（可能在其他地方被修改）
        loadSettings()
        loadTabs()
        loadHistory()
        loadBookmarks()
        
        #if DEBUG
        print("✅ 应用状态恢复完成")
        print("📊 当前状态: 标签页(\(tabs.count)), 历史记录(\(history.count)), 书签(\(bookmarks.count))")
        #endif
    }

    private func updateCurrentTabURL() {
        guard let currentTab = currentTab,
              let webView = currentTab.webView,
              let currentURL = webView.url else { return }

        // 更新标签页的URL为WebView当前的URL
        currentTab.url = currentURL
    }
    
    // MARK: - 标签页管理方法（优化版）
    @MainActor
    func createNewTab(url: URL? = nil) {
        if tabs.count >= maxTabsCount {
            // 关闭最旧的非活跃标签页
            if let oldestTab = tabs.first(where: { $0 != currentTab }) {
                closeTab(oldestTab)
            }
        }
        
        let newTab = NewBrowserTab(url: url)
        tabs.append(newTab)
        currentTabIndex = tabs.count - 1
        
        if let url = url {
            loadURL(url, in: newTab)
        }
        
        print("📱 [NewWebBrowserViewModel] 创建新标签页: \(newTab.id.uuidString.prefix(8))")
        saveTabs()
    }
    
    @MainActor
    func closeTab(at index: Int) {
        guard index >= 0 && index < tabs.count else { return }
        closeTab(tabs[index])
    }
    
    @MainActor
    func closeTab(_ tab: NewBrowserTab) {
        guard let index = tabs.firstIndex(of: tab) else { return }
        
        print("🗑️ [NewWebBrowserViewModel] 关闭标签页: \(tab.title)")
        
        // 清理资源
        tab.cleanup()
        
        // 从数组中移除
        tabs.remove(at: index)
        
        // 调整当前标签页索引
        if tabs.isEmpty {
            createNewTab()
        } else if currentTabIndex >= tabs.count {
            currentTabIndex = tabs.count - 1
        } else if index <= currentTabIndex && currentTabIndex > 0 {
            currentTabIndex -= 1
        }
        
        saveTabs()
    }

    @MainActor
    func selectTab(at index: Int) {
        guard index >= 0 && index < tabs.count else { return }
        
        currentTabIndex = index
        
        // 标记当前标签页为活跃
        currentTab?.markAsActive()
        
        print("🎯 [NewWebBrowserViewModel] 选择标签页: \(currentTab?.title ?? "未知")")
    }
    
    // MARK: - 浏览器控制方法
    @MainActor
    func loadURL(_ urlString: String) {
        guard let url = processURLInput(urlString) else {
            print("❌ [NewWebBrowserViewModel] 无效URL: \(urlString)")
            return
        }

        loadURL(url)
    }
    
    @MainActor
    func loadURL(_ url: URL, in tab: NewBrowserTab? = nil) {
        let targetTab = tab ?? currentTab
        
        guard let targetTab = targetTab else {
            // 如果没有当前标签页，创建一个新的
            createNewTab(url: url)
            return
        }
        
        print("🌐 [URL加载] 开始加载URL: \(url.absoluteString)")
        
        // 使用标签页的统一加载方法
        targetTab.loadURL(url, userAgent: effectiveUserAgent)
        
        // 添加到历史记录
        addToHistory(url: url, title: targetTab.title)
        
        // 确保当前标签页被更新
        if targetTab == currentTab {
            DispatchQueue.main.async { [weak self] in
                self?.objectWillChange.send()
            }
        }
    }
    
    // MARK: - 强制刷新当前标签页
    func refreshCurrentTab() {
        DispatchQueue.main.async { [weak self] in
            self?.objectWillChange.send()
        }
    }

    @MainActor
    func reload() {
        guard let currentTab = currentTab else { return }
        
        // 更新用户代理
        if let webView = currentTab.webView {
            webView.customUserAgent = effectiveUserAgent
            webView.reload()
        } else if let url = currentTab.url {
            // 如果WebView不存在，重新加载URL
            loadURL(url)
        }
    }
    
    func goBack() {
        guard let webView = currentTab?.webView, webView.canGoBack else { return }
        webView.goBack()
        // 立即更新按钮状态
        DispatchQueue.main.async {
            self.currentTab?.canGoBack = webView.canGoBack
            self.currentTab?.canGoForward = webView.canGoForward
        }
    }
    
    func goForward() {
        guard let webView = currentTab?.webView, webView.canGoForward else { return }
        webView.goForward()
        // 立即更新按钮状态
        DispatchQueue.main.async {
            self.currentTab?.canGoBack = webView.canGoBack
            self.currentTab?.canGoForward = webView.canGoForward
        }
    }
    
    func stopLoading() {
        guard let webView = currentTab?.webView else { return }
        webView.stopLoading()
    }

    // MARK: - URL 处理
    @MainActor
    func processSearch(_ query: String) -> URL {
        let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let searchURLString = currentSearchEngine.searchTemplate + encodedQuery
        return URL(string: searchURLString) ?? URL(string: "https://www.google.com")!
    }
    
    // MARK: - URL输入处理（简化版本）
    @MainActor
    func processURLInput(_ input: String) -> URL? {
        let trimmedInput = input.trimmingCharacters(in: .whitespacesAndNewlines)

        // 检查是否为有效URL
        if let url = URL(string: trimmedInput), url.scheme != nil {
            return isHTTPSOnlyEnabled ? enforceHTTPS(for: url) : url
        }

        // 尝试添加https://前缀
        if let url = URL(string: "https://\(trimmedInput)") {
            return url
        }

        // 作为搜索查询处理
        return processSearch(trimmedInput)
    }
    
    // MARK: - 地址栏建议
    func getAddressBarSuggestions(for input: String) -> [AddressSuggestion] {
        var suggestions: [AddressSuggestion] = []
        
        // 历史记录建议
        let historyMatches = history.filter { item in
            item.title.localizedCaseInsensitiveContains(input) ||
            item.url.localizedCaseInsensitiveContains(input)
        }.prefix(3)
        
        for item in historyMatches {
            suggestions.append(AddressSuggestion(
                type: .history,
                title: item.title,
                url: item.url,
                subtitle: item.url
            ))
        }
        
        // 书签建议
        let bookmarkMatches = bookmarks.filter { item in
            item.title.localizedCaseInsensitiveContains(input) ||
            item.url.localizedCaseInsensitiveContains(input)
        }.prefix(2)
        
        for item in bookmarkMatches {
            suggestions.append(AddressSuggestion(
                type: .bookmark,
                title: item.title,
                url: item.url,
                subtitle: "已收藏"
            ))
        }
        
        return suggestions
    }
    
    // MARK: - URL预加载
    func preloadURL(_ url: URL) {
        // 简单的预加载逻辑，可以在后台准备资源
        print("🔄 [NewWebBrowserViewModel] 预加载URL: \(url.absoluteString)")
    }

    // MARK: - 用户代理切换
    @MainActor
    func switchUserAgent(to userAgent: UserAgentType) {
        currentUserAgent = userAgent
        
        // 更新当前WebView的用户代理
        if let webView = currentTab?.webView {
            webView.customUserAgent = effectiveUserAgent
        }
        
        saveSettings()
        print("🔄 [NewWebBrowserViewModel] 用户代理已切换至: \(userAgent.displayName)")
    }

    // MARK: - 历史记录管理
    @MainActor
    func addToHistory(url: URL, title: String) {
        let historyItem = HistoryItem(title: title, url: url.absoluteString, visitDate: Date())

        // 避免重复添加相同的URL
        if !history.contains(where: { $0.url == url.absoluteString }) {
            history.insert(historyItem, at: 0)

            // 限制历史记录数量
            if history.count > 1000 {
                history = Array(history.prefix(1000))
            }

            saveHistory()
        }
    }
    
    @MainActor
    func clearHistory() {
        history.removeAll()
        saveHistory()
    }

    // MARK: - 书签管理
    private var bookmarkURLSet: Set<String> = []
    
    @MainActor
    func addBookmark(url: String, title: String) {
        let bookmark = BookmarkItem(title: title, url: url, category: "默认")
        
        // 在主线程同步执行
        bookmarks.append(bookmark)
        bookmarkURLSet.insert(url)
        saveBookmarks()
    }

    @MainActor
    func removeBookmark(_ bookmark: BookmarkItem) {
        // 在主线程同步执行
        bookmarks.removeAll { $0.id == bookmark.id }
        bookmarkURLSet.remove(bookmark.url)
        saveBookmarks()
    }

    @MainActor
    func isBookmarked(url: String) -> Bool {
        return bookmarkURLSet.contains(url)
    }
    
    private func rebuildBookmarkURLSet() {
        bookmarkURLSet = Set(bookmarks.map { $0.url })
    }

    // MARK: - 设置管理

    func switchSearchEngine(to searchEngine: BrowserSearchEngine) {
        currentSearchEngine = searchEngine
        saveSettings()
    }

    // MARK: - 安全功能（简化版本）
    @MainActor
    func enforceHTTPS(for url: URL) -> URL {
        if url.scheme == "http" {
            var components = URLComponents(url: url, resolvingAgainstBaseURL: false)
            components?.scheme = "https"
            return components?.url ?? url
        }
        return url
    }

    @MainActor
    func validateURLSecurity(_ url: URL) -> Bool {
        guard let scheme = url.scheme?.lowercased() else { return false }
        
        let urlString = url.absoluteString.lowercased()
        
        // 1. 允许标准浏览器协议
        let allowedSchemes = ["http", "https", "about", "data", "blob"]
        if !allowedSchemes.contains(scheme) {
            return false
        }
        
        // 2. 特殊处理about协议 - 只允许安全的about页面
        if scheme == "about" {
            let allowedAboutPages = ["about:blank"]
            return allowedAboutPages.contains(urlString)
        }
        
        // 3. 特殊处理data协议 - 只允许基本的data URL
        if scheme == "data" {
            // 只允许文本类型的data URL，防止恶意脚本
            return urlString.hasPrefix("data:text/") || urlString.hasPrefix("data:image/")
        }
        
        // 4. 特殊处理blob协议
        if scheme == "blob" {
            return true // blob URL通常是安全的
        }
        
        // 5. HTTP/HTTPS协议的额外检查
        if ["http", "https"].contains(scheme) {
            // 使用SecurityService进行更详细的安全检查
            return !SecurityService.shared.detectAdvancedThreats(url: url)
        }
        
        return false
    }

    // MARK: - WebView 创建和配置 (移除重复函数)
    

    

    


    // MARK: - DNS 预解析
    func preDNSLookup(for url: URL) {
        // 简化实现，移除未使用变量
        Task.detached {
            try? await URLSession.shared.data(from: url)
        }
    }

    // MARK: - 数据持久化
    private func saveSettings() {
        let settings: [String: Any] = [
            "currentUserAgent": currentUserAgent.rawValue,
            "currentSearchEngine": currentSearchEngine.rawValue
        ]
        userDefaults.set(settings, forKey: "NewBrowserSettings")
    }

    private func loadSettings() {
        guard let settings = userDefaults.dictionary(forKey: "NewBrowserSettings") else { return }

        if let userAgentRaw = settings["currentUserAgent"] as? String,
           let userAgent = UserAgentType(rawValue: userAgentRaw) {
            currentUserAgent = userAgent
        }

        if let searchEngineRaw = settings["currentSearchEngine"] as? String,
           let searchEngine = BrowserSearchEngine(rawValue: searchEngineRaw) {
            currentSearchEngine = searchEngine
        }
    }

    @MainActor
    func saveTabs() {
        #if DEBUG
        print("💾 保存标签页数据...")
        #endif
        
        // 只保存基本信息，不保存 WebView 实例
        let tabData = tabs.map { tab in
            [
                "id": tab.id.uuidString,
                "title": tab.title,
                "url": tab.url?.absoluteString ?? ""
            ]
        }
        
        userDefaults.set(tabData, forKey: "NewBrowserTabs")
        userDefaults.set(currentTabIndex, forKey: "NewBrowserCurrentTabIndex")
        
        #if DEBUG
        print("✅ 已保存 \(tabs.count) 个标签页，当前索引: \(currentTabIndex)")
        #endif
    }

    private func loadTabs() {
        #if DEBUG
        print("📂 加载标签页数据...")
        #endif
        
        guard let tabData = userDefaults.array(forKey: "NewBrowserTabs") as? [[String: String]] else {
            #if DEBUG
            print("⚠️ 没有找到已保存的标签页数据")
            #endif
            return
        }

        tabs = tabData.compactMap { data in
            // 恢复标签页ID
            guard let idString = data["id"], let id = UUID(uuidString: idString) else {
                #if DEBUG
                print("❌ 无效的标签页ID: \(data["id"] ?? "nil")")
                #endif
                return nil
            }

            // 恢复URL
            var url: URL? = nil
            if let urlString = data["url"], !urlString.isEmpty {
                url = URL(string: urlString)
            }

            // 创建标签页，使用原有ID
            let tab = NewBrowserTab(id: id, url: url)
            tab.title = data["title"] ?? "新标签页"

            return tab
        }

        // 恢复当前标签页索引
        currentTabIndex = userDefaults.integer(forKey: "NewBrowserCurrentTabIndex")
        if currentTabIndex >= tabs.count {
            currentTabIndex = max(0, tabs.count - 1)
        }
        
        #if DEBUG
        print("✅ 已加载 \(tabs.count) 个标签页，当前索引: \(currentTabIndex)")
        #endif
    }

    private func saveHistory() {
        let historyData = history.map { item in
            [
                "title": item.title,
                "url": item.url,
                "visitDate": item.visitDate.timeIntervalSince1970
            ]
        }
        userDefaults.set(historyData, forKey: "NewBrowserHistory")
    }

    private func loadHistory() {
        guard let historyData = userDefaults.array(forKey: "NewBrowserHistory") as? [[String: Any]] else { return }

        history = historyData.compactMap { data in
            guard let title = data["title"] as? String,
                  let url = data["url"] as? String,
                  let visitDateInterval = data["visitDate"] as? TimeInterval else { return nil }

            return HistoryItem(title: title, url: url, visitDate: Date(timeIntervalSince1970: visitDateInterval))
        }
    }

    private func saveBookmarks() {
        let bookmarkData = bookmarks.map { bookmark in
            [
                "id": bookmark.id.uuidString,
                "title": bookmark.title,
                "url": bookmark.url,
                "category": bookmark.category,
                "dateAdded": bookmark.dateAdded.timeIntervalSince1970
            ]
        }
        userDefaults.set(bookmarkData, forKey: "NewBrowserBookmarks")
    }

    private func loadBookmarks() {
        guard let bookmarkData = userDefaults.array(forKey: "NewBrowserBookmarks") as? [[String: Any]] else { 
            rebuildBookmarkURLSet()
            return 
        }

        bookmarks = bookmarkData.compactMap { data in
            guard let idString = data["id"] as? String,
                  let id = UUID(uuidString: idString),
                  let title = data["title"] as? String,
                  let url = data["url"] as? String,
                  let category = data["category"] as? String,
                  let dateAddedInterval = data["dateAdded"] as? TimeInterval else { return nil }

            return BookmarkItem(
                id: id,
                title: title,
                url: url,
                category: category,
                dateAdded: Date(timeIntervalSince1970: dateAddedInterval)
            )
        }
        
        // 重建URL集合以提高查询性能
        rebuildBookmarkURLSet()
    }

    // MARK: - 带安全检查的URL加载
    @MainActor
    func loadURLWithSecurityCheck(_ urlString: String) {
        guard let url = URL(string: urlString) else { return }
        
        #if DEBUG
        print("🔗 开始安全检查加载: \(urlString)")
        #endif
        
        // 检查是否在用户信任的网站列表中
        if isUserTrustedURL(url) {
            #if DEBUG
            print("✅ 用户已信任此网站，跳过安全检查")
            #endif
            loadURL(url)
            return
        }
        
        // 1. 基本安全检查
        if !validateURLSecurity(url) {
            #if DEBUG
            print("🚨 URL安全检查失败: \(urlString)")
            #endif
            showSecurityWarning(for: url, reason: "该网站被检测为恶意网站")
            return
        }

        // 2. HTTPS升级检查
        var finalURL = url
        if isHTTPSOnlyEnabled {
            finalURL = enforceHTTPS(for: url)
            #if DEBUG
            if finalURL != url {
                print("🔒 URL已升级为HTTPS: \(finalURL.absoluteString)")
            }
            #endif
        }
        
        // 6. 执行加载 - 使用currentTab的WebView
        if let currentTab = currentTab {
            let request = URLRequest(url: finalURL)
            currentTab.loadURL(request.url!, userAgent: effectiveUserAgent)
        }
    }
    
    @MainActor
    private func showNetworkPrivacyWarning(_ warningHTML: String) {
        if let currentTab = currentTab {
            currentTab.loadHTMLString(warningHTML, baseURL: nil, userAgent: effectiveUserAgent)
        }
    }
    
    private var currentHost: String? {
        return currentTab?.url?.host
    }
    
    // MARK: - 安全警告页面
    @MainActor
    private func showSecurityWarning(for url: URL, reason: String) {
        let warningHTML = generateSecurityWarningHTML(url: url, reason: reason)
        if let currentTab = currentTab {
            currentTab.loadHTMLString(warningHTML, baseURL: nil, userAgent: effectiveUserAgent)
        }
    }
    
    private func generateSecurityWarningHTML(url: URL, reason: String) -> String {
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>安全警告</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                    color: white;
                    text-align: center;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .container {
                    max-width: 600px;
                    margin: 0 auto;
                    background: rgba(255, 255, 255, 0.1);
                    padding: 40px;
                    border-radius: 20px;
                    backdrop-filter: blur(10px);
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                }
                .warning-icon {
                    font-size: 4em;
                    margin-bottom: 20px;
                    animation: pulse 2s infinite;
                }
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                    100% { transform: scale(1); }
                }
                h1 {
                    margin: 20px 0;
                    font-size: 2em;
                    font-weight: 600;
                }
                .reason {
                    background: rgba(255, 255, 255, 0.2);
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px 0;
                    border-left: 4px solid #fff;
                }
                .url {
                    word-break: break-all;
                    font-family: 'SF Mono', Monaco, monospace;
                    background: rgba(0, 0, 0, 0.3);
                    padding: 15px;
                    border-radius: 8px;
                    margin: 20px 0;
                }
                .buttons {
                    margin-top: 30px;
                    display: flex;
                    gap: 15px;
                    flex-wrap: wrap;
                    justify-content: center;
                }
                button {
                    background: rgba(255, 255, 255, 0.2);
                    border: 2px solid white;
                    color: white;
                    padding: 15px 30px;
                    border-radius: 25px;
                    font-size: 16px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    min-width: 160px;
                }
                button:hover {
                    background: white;
                    color: #ff6b6b;
                    transform: translateY(-2px);
                }
                .safe-button {
                    background: rgba(76, 175, 80, 0.4);
                    border-color: #4CAF50;
                }
                .safe-button:hover {
                    background: #4CAF50;
                    color: white;
                }
                .continue-button {
                    background: rgba(255, 152, 0, 0.4);
                    border-color: #FF9800;
                }
                .continue-button:hover {
                    background: #FF9800;
                    color: white;
                }
                .warning-details {
                    margin-top: 20px;
                    padding: 15px;
                    background: rgba(0, 0, 0, 0.2);
                    border-radius: 8px;
                    font-size: 14px;
                    text-align: left;
                }
                .risk-list {
                    margin: 10px 0;
                    padding-left: 20px;
                }
                .risk-list li {
                    margin: 5px 0;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="warning-icon">🛡️</div>
                <h1>安全警告</h1>
                <div class="reason">
                    <strong>警告原因：</strong><br>
                    \(reason)
                </div>
                <div class="url">
                    <strong>目标网址：</strong><br>
                    \(url.absoluteString)
                </div>
                
                <div class="warning-details">
                    <strong>⚠️ 继续访问可能面临的风险：</strong>
                    <ul class="risk-list">
                        <li>个人信息可能被窃取</li>
                        <li>设备可能感染恶意软件</li>
                        <li>可能遭受钓鱼攻击</li>
                        <li>银行账户等敏感信息面临风险</li>
                    </ul>
                </div>
                
                <div class="buttons">
                    <button class="safe-button" onclick="goBack()">
                        🔙 返回安全页面
                    </button>
                    <button class="continue-button" onclick="showConfirmDialog()">
                        ⚠️ 仍要继续访问
                    </button>
                </div>
            </div>
            
            <script>
                function goBack() {
                    if (window.history.length > 1) {
                        window.history.back();
                    } else {
                        // 如果没有历史记录，导航到主页
                        window.webkit.messageHandlers.securityWarning.postMessage({
                            action: 'goHome'
                        });
                    }
                }
                
                function showConfirmDialog() {
                    const confirmed = confirm(
                        '🚨 安全确认\\n\\n' +
                        '您确定要访问这个可能不安全的网站吗？\\n\\n' +
                        '继续访问可能会导致：\\n' +
                        '• 个人信息泄露\\n' +
                        '• 设备感染病毒\\n' +
                        '• 财务损失\\n\\n' +
                        '点击"确定"将信任该网站并继续访问。'
                    );
                    
                    if (confirmed) {
                        // 发送消息给原生代码，请求信任该网站
                        window.webkit.messageHandlers.securityWarning.postMessage({
                            action: 'trustAndContinue',
                            url: '\(url.absoluteString)'
                        });
                    }
                }
                
                // 页面加载完成后的初始化
                document.addEventListener('DOMContentLoaded', function() {
                    console.log('安全警告页面已加载');
                });
            </script>
        </body>
        </html>
        """
    }

    // MARK: - 用户信任的网站管理
    @Published var userTrustedURLs: Set<String> = []
    
    @MainActor
    func addToUserTrustedURLs(_ url: URL) {
        let urlString = url.absoluteString
        userTrustedURLs.insert(urlString)
        
        // 保存到 UserDefaults
        let trustedURLsArray = Array(userTrustedURLs)
        userDefaults.set(trustedURLsArray, forKey: "UserTrustedURLs")
        
        #if DEBUG
        print("✅ 网站已添加到用户信任列表: \(urlString)")
        #endif
    }
    
    private func isUserTrustedURL(_ url: URL) -> Bool {
        return userTrustedURLs.contains(url.absoluteString)
    }
    
    private func loadUserTrustedURLs() {
        if let trustedURLsArray = userDefaults.array(forKey: "UserTrustedURLs") as? [String] {
            userTrustedURLs = Set(trustedURLsArray)
        }
    }
    
    private func createInitialTab() {
        let newTab = NewBrowserTab()
        tabs.append(newTab)
        currentTabIndex = 0
    }
    
    private func ensureValidCurrentTabIndex() {
        if currentTabIndex >= tabs.count {
            currentTabIndex = max(0, tabs.count - 1)
        }
    }
}

// MARK: - 历史记录模型
struct HistoryItem: Identifiable {
    let id = UUID()
    let title: String
    let url: String
    let visitDate: Date
}

// MARK: - 书签模型
struct BookmarkItem: Identifiable {
    let id: UUID
    let title: String
    let url: String
    let category: String
    let dateAdded: Date

    init(id: UUID = UUID(), title: String, url: String, category: String, dateAdded: Date = Date()) {
        self.id = id
        self.title = title
        self.url = url
        self.category = category
        self.dateAdded = dateAdded
    }
}

// MARK: - 地址栏建议模型
enum SuggestionType {
    case history
    case bookmark
    case search
    case url
}

struct AddressSuggestion: Identifiable {
    let id = UUID()
    let type: SuggestionType
    let title: String
    let url: String
    let subtitle: String
    
    var icon: String {
        switch type {
        case .history: return "clock"
        case .bookmark: return "heart.fill"
        case .search: return "magnifyingglass"
        case .url: return "globe"
        }
    }
}
